import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { fetchMyCredentials } from '../../store/slices/credentialsSlice'
import CredentialUpload from '../../components/Credentials/CredentialUpload'
import CredentialsList from '../../components/Credentials/CredentialsList'
import CredentialDetail from '../../components/Credentials/CredentialDetail'
import {
  DocumentCheckIcon,
  PlusIcon,
  ListBulletIcon,
} from '@heroicons/react/24/outline'

const CredentialsPage = () => {
  const dispatch = useDispatch()
  const { selectedCredential, credentials } = useSelector((state) => state.credentials)
  const [activeTab, setActiveTab] = useState('list')

  const handleUploadComplete = (newCredential) => {
    // Refresh the credentials list after successful upload
    dispatch(fetchMyCredentials())
    // Switch to list view to show the new credential
    setActiveTab('list')
  }

  const tabs = [
    {
      id: 'list',
      name: 'My Credentials',
      icon: ListBulletIcon,
      count: credentials.length,
    },
    {
      id: 'upload',
      name: 'Upload New',
      icon: PlusIcon,
    },
  ]

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3">
          <DocumentCheckIcon className="h-8 w-8 text-healthcare-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Credentials Management
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Upload, manage, and track verification of your healthcare credentials
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">✓</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Verified
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {credentials.filter(c => c.status === 'verified').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">⏳</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Pending
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {credentials.filter(c => c.status === 'pending').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">⚠</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Expiring Soon
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {credentials.filter(c => {
                      if (!c.expiration_date) return false
                      const expDate = new Date(c.expiration_date)
                      const thirtyDaysFromNow = new Date()
                      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
                      return expDate <= thirtyDaysFromNow && expDate > new Date()
                    }).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📄</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {credentials.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${isActive
                    ? 'border-healthcare-500 text-healthcare-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <tab.icon
                  className={`
                    -ml-0.5 mr-2 h-5 w-5
                    ${isActive ? 'text-healthcare-500' : 'text-gray-400 group-hover:text-gray-500'}
                  `}
                />
                {tab.name}
                {tab.count !== undefined && (
                  <span
                    className={`
                      ml-2 py-0.5 px-2 rounded-full text-xs font-medium
                      ${isActive
                        ? 'bg-healthcare-100 text-healthcare-600'
                        : 'bg-gray-100 text-gray-900'
                      }
                    `}
                  >
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'upload' && (
          <CredentialUpload onUploadComplete={handleUploadComplete} />
        )}

        {activeTab === 'list' && (
          <CredentialsList />
        )}
      </div>

      {/* Credential Detail Modal */}
      {selectedCredential && (
        <CredentialDetail />
      )}
    </div>
  )
}

export default CredentialsPage
