import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import credentialsAPI from '../../services/credentialsAPI'

// Async thunks for credentials
export const fetchMyCredentials = createAsyncThunk(
  'credentials/fetchMyCredentials',
  async (_, { rejectWithValue }) => {
    try {
      const response = await credentialsAPI.getMyCredentials()
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch credentials')
    }
  }
)

export const uploadCredential = createAsyncThunk(
  'credentials/uploadCredential',
  async ({ file, credentialTypeId, useGoogleVision = true }, { rejectWithValue, dispatch }) => {
    try {
      const formData = new FormData()
      formData.append('document_file', file)
      if (credentialTypeId) {
        formData.append('credential_type_id', credentialTypeId)
      }
      formData.append('use_google_vision', useGoogleVision)

      const response = await credentialsAPI.uploadCredential(
        formData,
        (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          dispatch(setUploadProgress(progress))
        }
      )
      
      // Reset upload progress
      dispatch(setUploadProgress(0))
      
      return response.data
    } catch (error) {
      dispatch(setUploadProgress(0))
      return rejectWithValue(error.response?.data?.error || 'Upload failed')
    }
  }
)

export const fetchCredentialTypes = createAsyncThunk(
  'credentials/fetchCredentialTypes',
  async (_, { rejectWithValue }) => {
    try {
      const response = await credentialsAPI.getCredentialTypes()
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch credential types')
    }
  }
)

export const deleteCredential = createAsyncThunk(
  'credentials/deleteCredential',
  async (credentialId, { rejectWithValue }) => {
    try {
      await credentialsAPI.deleteCredential(credentialId)
      return credentialId
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete credential')
    }
  }
)

export const requestVerificationUpdate = createAsyncThunk(
  'credentials/requestVerificationUpdate',
  async ({ credentialId, notes }, { rejectWithValue }) => {
    try {
      const response = await credentialsAPI.requestVerificationUpdate(credentialId, notes)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to request verification update')
    }
  }
)

export const fetchDashboard = createAsyncThunk(
  'credentials/fetchDashboard',
  async (_, { rejectWithValue }) => {
    try {
      const response = await credentialsAPI.getDashboard()
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard data')
    }
  }
)

// Initial state
const initialState = {
  credentials: [],
  credentialTypes: [],
  dashboard: null,
  loading: false,
  uploading: false,
  uploadProgress: 0,
  error: null,
  selectedCredential: null,
}

// Credentials slice
const credentialsSlice = createSlice({
  name: 'credentials',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload
    },
    setSelectedCredential: (state, action) => {
      state.selectedCredential = action.payload
    },
    clearSelectedCredential: (state) => {
      state.selectedCredential = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch my credentials
      .addCase(fetchMyCredentials.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchMyCredentials.fulfilled, (state, action) => {
        state.loading = false
        state.credentials = action.payload.results || action.payload
      })
      .addCase(fetchMyCredentials.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      
      // Upload credential
      .addCase(uploadCredential.pending, (state) => {
        state.uploading = true
        state.error = null
      })
      .addCase(uploadCredential.fulfilled, (state, action) => {
        state.uploading = false
        state.credentials.unshift(action.payload)
      })
      .addCase(uploadCredential.rejected, (state, action) => {
        state.uploading = false
        state.error = action.payload
      })
      
      // Fetch credential types
      .addCase(fetchCredentialTypes.pending, (state) => {
        state.loading = true
      })
      .addCase(fetchCredentialTypes.fulfilled, (state, action) => {
        state.loading = false
        state.credentialTypes = action.payload.results || action.payload
      })
      .addCase(fetchCredentialTypes.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
      
      // Delete credential
      .addCase(deleteCredential.fulfilled, (state, action) => {
        state.credentials = state.credentials.filter(cred => cred.id !== action.payload)
      })
      .addCase(deleteCredential.rejected, (state, action) => {
        state.error = action.payload
      })
      
      // Fetch dashboard
      .addCase(fetchDashboard.pending, (state) => {
        state.loading = true
      })
      .addCase(fetchDashboard.fulfilled, (state, action) => {
        state.loading = false
        state.dashboard = action.payload
      })
      .addCase(fetchDashboard.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload
      })
  },
})

export const { 
  clearError, 
  setUploadProgress, 
  setSelectedCredential, 
  clearSelectedCredential 
} = credentialsSlice.actions

export default credentialsSlice.reducer
