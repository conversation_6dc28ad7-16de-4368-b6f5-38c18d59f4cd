import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

// Placeholder for facilities functionality
// This will be implemented when facilities features are developed

// Initial state
const initialState = {
  facilities: [],
  loading: false,
  error: null,
}

// Facilities slice
const facilitiesSlice = createSlice({
  name: 'facilities',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = facilitiesSlice.actions

export default facilitiesSlice.reducer
