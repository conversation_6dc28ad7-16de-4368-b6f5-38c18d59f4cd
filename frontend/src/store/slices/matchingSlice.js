import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

// Placeholder for matching functionality
// This will be implemented when matching features are developed

// Initial state
const initialState = {
  matches: [],
  loading: false,
  error: null,
}

// Matching slice
const matchingSlice = createSlice({
  name: 'matching',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = matchingSlice.actions

export default matchingSlice.reducer
