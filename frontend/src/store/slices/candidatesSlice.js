import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

// Placeholder for candidates functionality
// This will be implemented when candidates features are developed

// Initial state
const initialState = {
  candidates: [],
  loading: false,
  error: null,
}

// Candidates slice
const candidatesSlice = createSlice({
  name: 'candidates',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
})

export const { clearError } = candidatesSlice.actions

export default candidatesSlice.reducer
