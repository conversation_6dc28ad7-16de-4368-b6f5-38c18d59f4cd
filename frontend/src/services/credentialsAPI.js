import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refreshToken')
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          })

          const { access } = response.data
          localStorage.setItem('accessToken', access)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`
          return apiClient(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
        window.location.href = '/auth/login'
      }
    }

    return Promise.reject(error)
  }
)

const credentialsAPI = {
  // Get user's credentials
  getMyCredentials: () => apiClient.get('/credentials/my-credentials/'),
  
  // Get credential by ID
  getCredential: (id) => apiClient.get(`/credentials/my-credentials/${id}/`),
  
  // Upload credential document
  uploadCredential: (formData, onUploadProgress) => {
    return apiClient.post('/credentials/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress,
    })
  },
  
  // Get available credential types
  getCredentialTypes: () => apiClient.get('/credentials/available-types/'),
  
  // Get credential verification status
  getVerificationStatus: (credentialId) => 
    apiClient.get(`/credentials/status/${credentialId}/`),
  
  // Request verification update
  requestVerificationUpdate: (credentialId, notes) =>
    apiClient.post(`/credentials/request-update/${credentialId}/`, { notes }),
  
  // Delete credential
  deleteCredential: (id) => apiClient.delete(`/credentials/my-credentials/${id}/`),
  
  // Update credential
  updateCredential: (id, data) => apiClient.patch(`/credentials/my-credentials/${id}/`, data),
  
  // Get candidate dashboard data
  getDashboard: () => apiClient.get('/credentials/dashboard/'),
}

export default credentialsAPI
