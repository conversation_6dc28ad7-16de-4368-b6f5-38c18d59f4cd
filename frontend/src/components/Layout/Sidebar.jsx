import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  HomeIcon,
  DocumentCheckIcon,
  UserGroupIcon,
  BuildingOfficeIcon,
  MagnifyingGlassIcon,
  UserIcon,
} from '@heroicons/react/24/outline'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Credentials', href: '/dashboard/credentials', icon: DocumentCheckIcon },
  { name: 'Profile', href: '/dashboard/profile', icon: UserIcon },
  { name: 'Candidates', href: '/dashboard/candidates', icon: UserGroupIcon },
  { name: 'Facilities', href: '/dashboard/facilities', icon: BuildingOfficeIcon },
  { name: 'Matching', href: '/dashboard/matching', icon: MagnifyingGlassIcon },
]

const Sidebar = () => {
  const location = useLocation()

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
      <nav className="mt-8 px-4">
        <ul className="space-y-2">
          {navigation.map((item) => {
            const isActive = location.pathname === item.href
            return (
              <li key={item.name}>
                <Link
                  to={item.href}
                  className={`
                    group flex items-center px-2 py-2 text-sm font-medium rounded-md
                    ${isActive
                      ? 'bg-healthcare-100 text-healthcare-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                >
                  <item.icon
                    className={`
                      mr-3 h-6 w-6 flex-shrink-0
                      ${isActive ? 'text-healthcare-500' : 'text-gray-400 group-hover:text-gray-500'}
                    `}
                  />
                  {item.name}
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>
    </div>
  )
}

export default Sidebar
