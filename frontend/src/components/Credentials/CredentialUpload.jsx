import React, { useState, useRef, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { uploadCredential, fetchCredentialTypes } from '../../store/slices/credentialsSlice'
import toast from 'react-hot-toast'
import {
  CloudArrowUpIcon,
  DocumentIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'

const CredentialUpload = ({ onUploadComplete }) => {
  const dispatch = useDispatch()
  const { credentialTypes, uploading, uploadProgress, error } = useSelector((state) => state.credentials)
  
  const [dragActive, setDragActive] = useState(false)
  const [selectedFile, setSelectedFile] = useState(null)
  const [selectedCredentialType, setSelectedCredentialType] = useState('')
  const [useGoogleVision, setUseGoogleVision] = useState(true)
  
  const fileInputRef = useRef(null)

  useEffect(() => {
    dispatch(fetchCredentialTypes())
  }, [dispatch])

  const handleDrag = (e) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0])
    }
  }

  const handleFileSelect = (file) => {
    // Validate file type
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/tiff']
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a PDF, JPG, PNG, or TIFF file')
      return
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB')
      return
    }

    setSelectedFile(file)
  }

  const handleFileInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0])
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file to upload')
      return
    }

    try {
      const result = await dispatch(uploadCredential({
        file: selectedFile,
        credentialTypeId: selectedCredentialType || null,
        useGoogleVision,
      }))

      if (uploadCredential.fulfilled.match(result)) {
        toast.success('Credential uploaded successfully!')
        setSelectedFile(null)
        setSelectedCredentialType('')
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
        if (onUploadComplete) {
          onUploadComplete(result.payload)
        }
      } else {
        toast.error(result.payload || 'Upload failed')
      }
    } catch (error) {
      toast.error('Upload failed')
    }
  }

  const removeSelectedFile = () => {
    setSelectedFile(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Upload New Credential
      </h3>

      {/* Credential Type Selection */}
      <div className="mb-4">
        <label htmlFor="credentialType" className="block text-sm font-medium text-gray-700 mb-2">
          Credential Type (Optional)
        </label>
        <select
          id="credentialType"
          value={selectedCredentialType}
          onChange={(e) => setSelectedCredentialType(e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-healthcare-500 focus:border-healthcare-500 sm:text-sm"
        >
          <option value="">Auto-detect from document</option>
          {credentialTypes.map((type) => (
            <option key={type.id} value={type.id}>
              {type.name} ({type.category})
            </option>
          ))}
        </select>
      </div>

      {/* OCR Options */}
      <div className="mb-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={useGoogleVision}
            onChange={(e) => setUseGoogleVision(e.target.checked)}
            className="rounded border-gray-300 text-healthcare-600 focus:ring-healthcare-500"
          />
          <span className="ml-2 text-sm text-gray-700">
            Use Google Vision API for better OCR accuracy
          </span>
        </label>
      </div>

      {/* File Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center
          ${dragActive 
            ? 'border-healthcare-500 bg-healthcare-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${uploading ? 'pointer-events-none opacity-50' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf,.jpg,.jpeg,.png,.tiff"
          onChange={handleFileInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={uploading}
        />

        {selectedFile ? (
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <DocumentIcon className="h-8 w-8 text-gray-400" />
              <div className="text-left">
                <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-xs text-gray-500">{formatFileSize(selectedFile.size)}</p>
              </div>
              <button
                onClick={removeSelectedFile}
                className="text-gray-400 hover:text-gray-600"
                disabled={uploading}
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {uploading && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-healthcare-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            )}

            <button
              onClick={handleUpload}
              disabled={uploading}
              className="w-full bg-healthcare-600 text-white py-2 px-4 rounded-md hover:bg-healthcare-700 focus:outline-none focus:ring-2 focus:ring-healthcare-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {uploading ? `Uploading... ${uploadProgress}%` : 'Upload Credential'}
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
            <div>
              <p className="text-sm text-gray-600">
                <span className="font-medium text-healthcare-600">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-gray-500">
                PDF, JPG, PNG, or TIFF up to 10MB
              </p>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="mt-4 text-sm text-red-600">
          {error}
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p>
          <strong>Supported documents:</strong> Professional licenses, certifications, 
          education certificates, training records, and background checks.
        </p>
        <p className="mt-1">
          Our AI will automatically extract key information from your documents 
          and verify them against official sources when possible.
        </p>
      </div>
    </div>
  )
}

export default CredentialUpload
