import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { 
  fetchMyCredentials, 
  deleteCredential, 
  requestVerificationUpdate,
  setSelectedCredential 
} from '../../store/slices/credentialsSlice'
import toast from 'react-hot-toast'
import {
  DocumentIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  EyeIcon,
  TrashIcon,
  ChatBubbleLeftIcon,
} from '@heroicons/react/24/outline'

const CredentialsList = () => {
  const dispatch = useDispatch()
  const { credentials, loading } = useSelector((state) => state.credentials)
  const [showUpdateModal, setShowUpdateModal] = useState(false)
  const [selectedCredentialId, setSelectedCredentialId] = useState(null)
  const [updateNotes, setUpdateNotes] = useState('')

  useEffect(() => {
    dispatch(fetchMyCredentials())
  }, [dispatch])

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />
      case 'expired':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />
      case 'invalid':
      case 'revoked':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'verified':
        return 'Verified'
      case 'pending':
        return 'Pending Verification'
      case 'expired':
        return 'Expired'
      case 'invalid':
        return 'Invalid'
      case 'revoked':
        return 'Revoked'
      default:
        return 'Unknown'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'expired':
        return 'bg-orange-100 text-orange-800'
      case 'invalid':
      case 'revoked':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleDelete = async (credentialId) => {
    if (window.confirm('Are you sure you want to delete this credential?')) {
      try {
        const result = await dispatch(deleteCredential(credentialId))
        if (deleteCredential.fulfilled.match(result)) {
          toast.success('Credential deleted successfully')
        } else {
          toast.error('Failed to delete credential')
        }
      } catch (error) {
        toast.error('Failed to delete credential')
      }
    }
  }

  const handleRequestUpdate = async () => {
    if (!selectedCredentialId || !updateNotes.trim()) {
      toast.error('Please provide notes for the verification update request')
      return
    }

    try {
      const result = await dispatch(requestVerificationUpdate({
        credentialId: selectedCredentialId,
        notes: updateNotes,
      }))

      if (requestVerificationUpdate.fulfilled.match(result)) {
        toast.success('Verification update request sent successfully')
        setShowUpdateModal(false)
        setSelectedCredentialId(null)
        setUpdateNotes('')
      } else {
        toast.error('Failed to send verification update request')
      }
    } catch (error) {
      toast.error('Failed to send verification update request')
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-healthcare-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          My Credentials ({credentials.length})
        </h3>
      </div>

      {credentials.length === 0 ? (
        <div className="p-6 text-center">
          <DocumentIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No credentials</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by uploading your first credential.
          </p>
        </div>
      ) : (
        <div className="divide-y divide-gray-200">
          {credentials.map((credential) => (
            <div key={credential.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <DocumentIcon className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {credential.credential_type?.name || 'Unknown Type'}
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(credential.status)}`}>
                        {getStatusIcon(credential.status)}
                        <span className="ml-1">{getStatusText(credential.status)}</span>
                      </span>
                    </div>
                    <div className="mt-1 text-sm text-gray-500">
                      <p>
                        {credential.license_number && (
                          <span>License: {credential.license_number} • </span>
                        )}
                        {credential.holder_name && (
                          <span>Holder: {credential.holder_name} • </span>
                        )}
                        Uploaded: {formatDate(credential.created_at)}
                      </p>
                      {credential.expiration_date && (
                        <p>Expires: {formatDate(credential.expiration_date)}</p>
                      )}
                      {credential.issuing_authority && (
                        <p>Issued by: {credential.issuing_authority}</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => dispatch(setSelectedCredential(credential))}
                    className="text-gray-400 hover:text-gray-600"
                    title="View details"
                  >
                    <EyeIcon className="h-5 w-5" />
                  </button>

                  {credential.status === 'pending' && (
                    <button
                      onClick={() => {
                        setSelectedCredentialId(credential.id)
                        setShowUpdateModal(true)
                      }}
                      className="text-blue-400 hover:text-blue-600"
                      title="Request verification update"
                    >
                      <ChatBubbleLeftIcon className="h-5 w-5" />
                    </button>
                  )}

                  <button
                    onClick={() => handleDelete(credential.id)}
                    className="text-red-400 hover:text-red-600"
                    title="Delete credential"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {credential.verification_notes && (
                <div className="mt-3 p-3 bg-blue-50 rounded-md">
                  <p className="text-sm text-blue-800">
                    <strong>Verification Notes:</strong> {credential.verification_notes}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Update Request Modal */}
      {showUpdateModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Request Verification Update
              </h3>
              <textarea
                value={updateNotes}
                onChange={(e) => setUpdateNotes(e.target.value)}
                placeholder="Please provide additional information or ask questions about the verification process..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-healthcare-500 focus:border-healthcare-500"
                rows={4}
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => {
                    setShowUpdateModal(false)
                    setSelectedCredentialId(null)
                    setUpdateNotes('')
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRequestUpdate}
                  className="px-4 py-2 text-sm font-medium text-white bg-healthcare-600 rounded-md hover:bg-healthcare-700"
                >
                  Send Request
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CredentialsList
