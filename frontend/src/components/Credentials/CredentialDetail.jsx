import React from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { clearSelectedCredential } from '../../store/slices/credentialsSlice'
import {
  XMarkIcon,
  DocumentIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  IdentificationIcon,
  UserIcon,
} from '@heroicons/react/24/outline'

const CredentialDetail = () => {
  const dispatch = useDispatch()
  const { selectedCredential } = useSelector((state) => state.credentials)

  if (!selectedCredential) return null

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />
      case 'pending':
        return <ClockIcon className="h-6 w-6 text-yellow-500" />
      case 'expired':
        return <ExclamationTriangleIcon className="h-6 w-6 text-orange-500" />
      case 'invalid':
      case 'revoked':
        return <XCircleIcon className="h-6 w-6 text-red-500" />
      default:
        return <ClockIcon className="h-6 w-6 text-gray-500" />
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'verified':
        return 'Verified'
      case 'pending':
        return 'Pending Verification'
      case 'expired':
        return 'Expired'
      case 'invalid':
        return 'Invalid'
      case 'revoked':
        return 'Revoked'
      default:
        return 'Unknown'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center space-x-3">
            <DocumentIcon className="h-8 w-8 text-gray-400" />
            <div>
              <h3 className="text-xl font-medium text-gray-900">
                {selectedCredential.credential_type?.name || 'Unknown Credential Type'}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(selectedCredential.status)}
                <span className="text-sm text-gray-600">
                  {getStatusText(selectedCredential.status)}
                </span>
              </div>
            </div>
          </div>
          <button
            onClick={() => dispatch(clearSelectedCredential())}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-6">
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Basic Information
              </h4>
              <div className="space-y-3">
                {selectedCredential.license_number && (
                  <div className="flex items-center space-x-3">
                    <IdentificationIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">License Number</p>
                      <p className="text-sm text-gray-600">{selectedCredential.license_number}</p>
                    </div>
                  </div>
                )}

                {selectedCredential.holder_name && (
                  <div className="flex items-center space-x-3">
                    <UserIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Holder Name</p>
                      <p className="text-sm text-gray-600">{selectedCredential.holder_name}</p>
                    </div>
                  </div>
                )}

                {selectedCredential.issuing_authority && (
                  <div className="flex items-center space-x-3">
                    <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Issuing Authority</p>
                      <p className="text-sm text-gray-600">{selectedCredential.issuing_authority}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <CalendarIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Issue Date</p>
                    <p className="text-sm text-gray-600">
                      {formatDate(selectedCredential.issue_date)}
                    </p>
                  </div>
                </div>

                {selectedCredential.expiration_date && (
                  <div className="flex items-center space-x-3">
                    <CalendarIcon className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Expiration Date</p>
                      <p className="text-sm text-gray-600">
                        {formatDate(selectedCredential.expiration_date)}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Verification Information */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Verification Details
              </h4>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900">Verification Date</p>
                  <p className="text-sm text-gray-600">
                    {formatDateTime(selectedCredential.verification_date)}
                  </p>
                </div>

                {selectedCredential.verification_source && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Verification Source</p>
                    <p className="text-sm text-gray-600">{selectedCredential.verification_source}</p>
                  </div>
                )}

                {selectedCredential.verification_notes && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Verification Notes</p>
                    <p className="text-sm text-gray-600">{selectedCredential.verification_notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* AI Extracted Data */}
          <div className="space-y-6">
            {selectedCredential.ai_extracted_data && (
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">
                  AI Analysis
                </h4>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  {selectedCredential.ai_extracted_data.extraction_confidence && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">Extraction Confidence</p>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{
                              width: `${(selectedCredential.ai_extracted_data.extraction_confidence * 100)}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">
                          {Math.round(selectedCredential.ai_extracted_data.extraction_confidence * 100)}%
                        </span>
                      </div>
                    </div>
                  )}

                  {selectedCredential.ai_extracted_data.ocr_confidence && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">OCR Confidence</p>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-600 h-2 rounded-full"
                            style={{
                              width: `${(selectedCredential.ai_extracted_data.ocr_confidence * 100)}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">
                          {Math.round(selectedCredential.ai_extracted_data.ocr_confidence * 100)}%
                        </span>
                      </div>
                    </div>
                  )}

                  {selectedCredential.ai_extracted_data.ocr_method && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">OCR Method</p>
                      <p className="text-sm text-gray-600 capitalize">
                        {selectedCredential.ai_extracted_data.ocr_method}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Document Information */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Document Information
              </h4>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900">Uploaded</p>
                  <p className="text-sm text-gray-600">
                    {formatDateTime(selectedCredential.created_at)}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-900">Last Updated</p>
                  <p className="text-sm text-gray-600">
                    {formatDateTime(selectedCredential.updated_at)}
                  </p>
                </div>

                {selectedCredential.document_file && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Document File</p>
                    <a
                      href={selectedCredential.document_file}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-healthcare-600 hover:text-healthcare-500"
                    >
                      View Original Document
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-6 flex justify-end">
          <button
            onClick={() => dispatch(clearSelectedCredential())}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default CredentialDetail
