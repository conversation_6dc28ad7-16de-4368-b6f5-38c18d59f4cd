{% extends "admin/change_list.html" %}
{% load static %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .workflow-navigation {
            background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 0 0 8px 8px;
        }
        .workflow-nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .workflow-nav-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 6px;
            text-decoration: none;
            color: white;
            transition: background-color 0.2s;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .workflow-nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
        }
        .workflow-nav-title {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 5px;
        }
        .workflow-nav-desc {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .quick-stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .quick-stat {
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .quick-stat-number {
            font-weight: bold;
            font-size: 1.2em;
        }
        .workflow-alerts {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .workflow-alerts h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .alert-item {
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 4px;
        }
    </style>
{% endblock %}

{% block content_title %}
    <div class="workflow-navigation">
        <h1>Credential Verification Management</h1>
        <p>Comprehensive workflow for healthcare credential verification and compliance</p>
        
        <div class="quick-stats">
            <div class="quick-stat">
                <div class="quick-stat-number">{{ quick_stats.pending_count }}</div>
                <div>Pending Review</div>
            </div>
            <div class="quick-stat">
                <div class="quick-stat-number">{{ quick_stats.verified_today }}</div>
                <div>Verified Today</div>
            </div>
            <div class="quick-stat">
                <div class="quick-stat-number">{{ quick_stats.high_priority_count }}</div>
                <div>High Priority</div>
            </div>
        </div>
        
        <div class="workflow-nav-grid">
            {% for link in workflow_links %}
            <a href="{{ link.url }}" class="workflow-nav-item">
                <div class="workflow-nav-title">{{ link.title }}</div>
                <div class="workflow-nav-desc">{{ link.description }}</div>
            </a>
            {% endfor %}
        </div>
    </div>

    {% if quick_stats.high_priority_count > 0 %}
    <div class="workflow-alerts">
        <h4>⚠️ Attention Required</h4>
        <div class="alert-item">
            <strong>{{ quick_stats.high_priority_count }} high-priority credential(s)</strong> have been pending for over a week.
            <a href="{% url 'admin:credentials_verification_queue' %}?priority=high">Review now →</a>
        </div>
    </div>
    {% endif %}
{% endblock %}

{% block result_list %}
    <!-- Enhanced result list with workflow context -->
    <div class="results-header" style="background: #f8f9fa; padding: 15px; margin-bottom: 15px; border-radius: 6px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h3 style="margin: 0;">Credential Records</h3>
                <p style="margin: 5px 0 0 0; color: #666;">
                    Manage individual credential records and verification status
                </p>
            </div>
            <div style="display: flex; gap: 10px;">
                <a href="{% url 'admin:credentials_verification_dashboard' %}" class="button">
                    📊 Dashboard
                </a>
                <a href="{% url 'admin:credentials_verification_queue' %}" class="button">
                    📋 Queue
                </a>
            </div>
        </div>
    </div>
    
    {{ block.super }}
{% endblock %}

{% block pagination %}
    {{ block.super }}
    
    <!-- Workflow tips -->
    <div style="background: #e3f2fd; padding: 15px; margin-top: 20px; border-radius: 6px; border-left: 4px solid #2196f3;">
        <h4 style="margin: 0 0 10px 0; color: #1976d2;">💡 Workflow Tips</h4>
        <ul style="margin: 0; padding-left: 20px; color: #1976d2;">
            <li>Use the <strong>Verification Dashboard</strong> for real-time overview and quick actions</li>
            <li>Process credentials efficiently with the <strong>Verification Queue</strong> priority system</li>
            <li>Monitor team performance and compliance with <strong>Analytics</strong></li>
            <li>Use bulk actions to verify multiple credentials at once</li>
            <li>Check the AI confidence scores to prioritize manual review</li>
        </ul>
    </div>
{% endblock %}

{% block extrajs %}
    {{ block.super }}
    <script>
        // Enhanced admin functionality
        $(document).ready(function() {
            // Add keyboard shortcuts info
            if (!localStorage.getItem('keyboard_shortcuts_shown')) {
                setTimeout(function() {
                    if (confirm('💡 Tip: Use Ctrl+A to select all visible items, Escape to deselect all. Show this tip again?')) {
                        localStorage.setItem('keyboard_shortcuts_shown', 'true');
                    }
                }, 2000);
            }
            
            // Add real-time updates indicator
            const header = $('.workflow-navigation h1');
            const updateIndicator = $('<span id="update-indicator" style="margin-left: 10px; opacity: 0.7;">●</span>');
            header.append(updateIndicator);
            
            // Pulse indicator every 30 seconds to show live updates
            setInterval(function() {
                updateIndicator.fadeOut(200).fadeIn(200);
            }, 30000);
            
            // Enhanced filter persistence
            $('.admin-filter-form select, .admin-filter-form input').change(function() {
                const filterId = $(this).attr('name');
                const filterValue = $(this).val();
                sessionStorage.setItem(`admin_filter_${filterId}`, filterValue);
            });
            
            // Auto-save form data
            let formChanged = false;
            $('form input, form select, form textarea').change(function() {
                formChanged = true;
            });
            
            $(window).on('beforeunload', function() {
                if (formChanged) {
                    return 'You have unsaved changes. Are you sure you want to leave?';
                }
            });
            
            $('form').submit(function() {
                formChanged = false;
            });
        });
    </script>
{% endblock %}
