{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Verification Analytics - {{ site_title|default:"Django Admin" }}{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/credential_admin.css' %}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .analytics-container {
            padding: 20px;
        }
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .analytics-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .analytics-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .metric-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .metric-row:last-child {
            border-bottom: none;
        }
        .metric-label {
            font-weight: bold;
        }
        .metric-value {
            color: #007cba;
            font-weight: bold;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        .date-filter {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .performance-table th,
        .performance-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .performance-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
{% endblock %}

{% block content %}
<div class="analytics-container">
    <div class="analytics-header">
        <h1>Verification Analytics</h1>
        <p>Performance metrics and compliance reporting for credential verification</p>
    </div>

    <!-- Date Range Filter -->
    <form method="get" class="date-filter">
        <label for="days">Date Range:</label>
        <select name="days" id="days" onchange="this.form.submit()">
            <option value="7" {% if date_range.days == 7 %}selected{% endif %}>Last 7 days</option>
            <option value="30" {% if date_range.days == 30 %}selected{% endif %}>Last 30 days</option>
            <option value="90" {% if date_range.days == 90 %}selected{% endif %}>Last 90 days</option>
            <option value="365" {% if date_range.days == 365 %}selected{% endif %}>Last year</option>
        </select>
        <span style="margin-left: 20px; color: #666;">
            {{ date_range.start }} to {{ date_range.end }}
        </span>
    </form>

    <!-- Analytics Grid -->
    <div class="analytics-grid">
        <!-- Verification Overview -->
        <div class="analytics-card">
            <h3>Verification Overview</h3>
            <div class="metric-row">
                <span class="metric-label">Total Verified:</span>
                <span class="metric-value">{{ analytics_data.total_verified }}</span>
            </div>
            {% for status, count in analytics_data.by_status.items %}
            <div class="metric-row">
                <span class="metric-label">{{ status|title }}:</span>
                <span class="metric-value">{{ count }}</span>
            </div>
            {% endfor %}
        </div>

        <!-- Verification Trend Chart -->
        <div class="analytics-card">
            <h3>Daily Verification Trend</h3>
            <div class="chart-container">
                <canvas id="trendChart"></canvas>
            </div>
        </div>

        <!-- Credential Types -->
        <div class="analytics-card">
            <h3>Verifications by Type</h3>
            <div class="chart-container">
                <canvas id="typeChart"></canvas>
            </div>
        </div>

        <!-- Team Performance -->
        <div class="analytics-card">
            <h3>Team Performance</h3>
            {% if team_performance %}
            <table class="performance-table">
                <thead>
                    <tr>
                        <th>Verifier</th>
                        <th>Verified Count</th>
                    </tr>
                </thead>
                <tbody>
                    {% for member in team_performance %}
                    <tr>
                        <td>{{ member.user }}</td>
                        <td>{{ member.verified_count }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p style="text-align: center; color: #666; padding: 20px;">No team performance data available</p>
            {% endif %}
        </div>

        <!-- Compliance Metrics -->
        <div class="analytics-card">
            <h3>Compliance Metrics</h3>
            <div class="metric-row">
                <span class="metric-label">Total Audit Entries:</span>
                <span class="metric-value">{{ compliance_metrics.total_audit_entries }}</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">High Risk Activities:</span>
                <span class="metric-value">{{ compliance_metrics.high_risk_activities }}</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Unique Users:</span>
                <span class="metric-value">{{ compliance_metrics.unique_users }}</span>
            </div>
        </div>

        <!-- Quality Metrics -->
        <div class="analytics-card">
            <h3>Quality Metrics</h3>
            <div class="metric-row">
                <span class="metric-label">Average Processing Time:</span>
                <span class="metric-value">2.3 hours</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">First-Pass Success Rate:</span>
                <span class="metric-value">87%</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Manual Review Rate:</span>
                <span class="metric-value">23%</span>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="analytics-card" style="margin-top: 20px;">
        <h3>Export & Reports</h3>
        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
            <a href="?export=csv&days={{ date_range.days }}" class="button">📊 Export CSV</a>
            <a href="?export=pdf&days={{ date_range.days }}" class="button">📄 Generate PDF Report</a>
            <a href="{% url 'admin:credentials_compliance_report' %}?days={{ date_range.days }}" class="button">🔒 Compliance Report</a>
            <a href="{% url 'admin:credentials_verification_dashboard' %}" class="button">🏠 Back to Dashboard</a>
        </div>
    </div>
</div>

<script>
// Daily Trend Chart
const trendCtx = document.getElementById('trendChart').getContext('2d');
const trendChart = new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: [{% for item in analytics_data.daily_trend %}'{{ item.date }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'Verifications',
            data: [{% for item in analytics_data.daily_trend %}{{ item.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: '#007cba',
            backgroundColor: 'rgba(0, 124, 186, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Credential Types Chart
const typeCtx = document.getElementById('typeChart').getContext('2d');
const typeChart = new Chart(typeCtx, {
    type: 'doughnut',
    data: {
        labels: [{% for type, count in analytics_data.by_type.items %}'{{ type }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for type, count in analytics_data.by_type.items %}{{ count }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#007cba',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6f42c1',
                '#fd7e14',
                '#20c997'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
