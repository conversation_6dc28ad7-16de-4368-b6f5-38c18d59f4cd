{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Verification Dashboard - {{ site_title|default:"Django Admin" }}{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/credential_admin.css' %}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .dashboard-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .dashboard-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        .stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007cba;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007cba;
        }
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        .pending-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .pending-item:last-child {
            border-bottom: none;
        }
        .priority-high { border-left: 4px solid #dc3545; }
        .priority-medium { border-left: 4px solid #ffc107; }
        .priority-low { border-left: 4px solid #28a745; }
        .quick-actions {
            display: flex;
            gap: 5px;
        }
        .btn-quick {
            padding: 4px 8px;
            font-size: 0.8em;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-verify { background: #28a745; color: white; }
        .btn-reject { background: #dc3545; color: white; }
        .btn-flag { background: #ffc107; color: black; }
        .alert-item {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .alert-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .refresh-indicator {
            display: inline-block;
            margin-left: 10px;
            color: #007cba;
            cursor: pointer;
        }
    </style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>
            Verification Dashboard
            <span class="refresh-indicator" onclick="refreshDashboard()" title="Refresh Data">
                🔄
            </span>
        </h1>
        <p>Real-time credential verification workflow management</p>
    </div>

    <!-- Statistics Overview -->
    <div class="stat-grid">
        <div class="stat-card">
            <div class="stat-number" id="total-credentials">{{ stats.total_credentials }}</div>
            <div class="stat-label">Total Credentials</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="pending-count" style="color: #ffc107;">{{ stats.pending_count }}</div>
            <div class="stat-label">Pending Review</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="verified-count" style="color: #28a745;">{{ stats.verified_count }}</div>
            <div class="stat-label">Verified</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="verification-rate">{{ stats.verification_rate }}%</div>
            <div class="stat-label">Verification Rate</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="today-verified" style="color: #007cba;">{{ stats.today_verified }}</div>
            <div class="stat-label">Verified Today</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="avg-processing-time">{{ stats.avg_processing_time }}h</div>
            <div class="stat-label">Avg Processing Time</div>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Pending Credentials -->
        <div class="dashboard-card">
            <h3>
                Pending Credentials
                <a href="{% url 'admin:credentials_verification_queue' %}" style="float: right; font-size: 0.8em;">View All</a>
            </h3>
            <div id="pending-credentials">
                {% for item in pending_credentials %}
                <div class="pending-item priority-{{ item.priority_label|lower }}">
                    <div>
                        <strong>{{ item.credential_type.name }}</strong><br>
                        <small>{{ item.user.get_full_name|default:item.user.email }}</small><br>
                        <small style="color: #666;">{{ item.created_at|timesince }} ago</small>
                    </div>
                    <div class="quick-actions">
                        <button class="btn-quick btn-verify" onclick="quickAction('{{ item.id }}', 'verify')">✓</button>
                        <button class="btn-quick btn-reject" onclick="quickAction('{{ item.id }}', 'reject')">✗</button>
                        <button class="btn-quick btn-flag" onclick="quickAction('{{ item.id }}', 'flag')">🏴</button>
                    </div>
                </div>
                {% empty %}
                <p style="text-align: center; color: #666; padding: 20px;">No pending credentials</p>
                {% endfor %}
            </div>
        </div>

        <!-- AI Analysis Alerts -->
        <div class="dashboard-card">
            <h3>AI Analysis Alerts</h3>
            <div id="ai-alerts">
                {% for alert in ai_alerts %}
                <div class="alert-item alert-{{ alert.severity }}">
                    <strong>{{ alert.credential.credential_type.name }}</strong><br>
                    {{ alert.message }}<br>
                    <small>{{ alert.credential.user.get_full_name|default:alert.credential.user.email }}</small>
                </div>
                {% empty %}
                <p style="text-align: center; color: #666; padding: 20px;">No alerts</p>
                {% endfor %}
            </div>
        </div>

        <!-- Queue Metrics -->
        <div class="dashboard-card">
            <h3>Queue Metrics</h3>
            <div>
                <p><strong>Total Pending:</strong> {{ queue_metrics.total_pending }}</p>
                <div style="margin-top: 15px;">
                    <div style="margin-bottom: 8px;">
                        <span>Under 1 hour:</span>
                        <span style="float: right; font-weight: bold;">{{ queue_metrics.age_distribution.under_1_hour }}</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span>Under 24 hours:</span>
                        <span style="float: right; font-weight: bold;">{{ queue_metrics.age_distribution.under_24_hours }}</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span>Under 1 week:</span>
                        <span style="float: right; font-weight: bold;">{{ queue_metrics.age_distribution.under_week }}</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span>Over 1 week:</span>
                        <span style="float: right; font-weight: bold; color: #dc3545;">{{ queue_metrics.age_distribution.over_week }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="dashboard-card">
            <h3>Recent Activity</h3>
            <div id="recent-activity">
                {% for activity in recent_activity %}
                <div style="padding: 8px 0; border-bottom: 1px solid #eee;">
                    <div style="font-weight: bold;">{{ activity.action }}</div>
                    <div style="font-size: 0.9em; color: #666;">
                        by {{ activity.user }} - {{ activity.timestamp|timesince }} ago
                    </div>
                </div>
                {% empty %}
                <p style="text-align: center; color: #666; padding: 20px;">No recent activity</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="dashboard-card" style="margin-top: 20px;">
        <h3>Quick Actions</h3>
        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
            <a href="{% url 'admin:credentials_verification_queue' %}" class="button">View Verification Queue</a>
            <a href="{% url 'admin:credentials_verification_analytics' %}" class="button">View Analytics</a>
            <a href="{% url 'admin:credentials_credential_changelist' %}" class="button">Manage Credentials</a>
            <a href="{% url 'admin:credentials_compliance_report' %}" class="button">Compliance Report</a>
        </div>
    </div>
</div>

<!-- Quick Action Modal -->
<div id="quickActionModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; min-width: 400px;">
        <h3 id="modalTitle">Verify Credential</h3>
        <form id="quickActionForm">
            <div style="margin-bottom: 15px;">
                <label for="actionNotes">Notes (optional):</label>
                <textarea id="actionNotes" name="notes" rows="3" style="width: 100%; margin-top: 5px;"></textarea>
            </div>
            <div style="text-align: right;">
                <button type="button" onclick="closeModal()" style="margin-right: 10px;">Cancel</button>
                <button type="submit" id="confirmAction" class="button">Confirm</button>
            </div>
        </form>
    </div>
</div>

<script>
let currentCredentialId = null;
let currentAction = null;

function quickAction(credentialId, action) {
    currentCredentialId = credentialId;
    currentAction = action;
    
    const modal = document.getElementById('quickActionModal');
    const title = document.getElementById('modalTitle');
    const confirmBtn = document.getElementById('confirmAction');
    
    switch(action) {
        case 'verify':
            title.textContent = 'Verify Credential';
            confirmBtn.textContent = 'Verify';
            confirmBtn.className = 'button btn-verify';
            break;
        case 'reject':
            title.textContent = 'Reject Credential';
            confirmBtn.textContent = 'Reject';
            confirmBtn.className = 'button btn-reject';
            break;
        case 'flag':
            title.textContent = 'Flag for Review';
            confirmBtn.textContent = 'Flag';
            confirmBtn.className = 'button btn-flag';
            break;
    }
    
    modal.style.display = 'block';
}

function closeModal() {
    document.getElementById('quickActionModal').style.display = 'none';
    document.getElementById('actionNotes').value = '';
}

document.getElementById('quickActionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const notes = document.getElementById('actionNotes').value;
    const formData = new FormData();
    formData.append('action', currentAction);
    formData.append('notes', notes);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch(`/admin/credentials/quick-verify/${currentCredentialId}/`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal();
            refreshDashboard();
            alert(data.message);
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
});

function refreshDashboard() {
    fetch('/admin/credentials/ajax/dashboard-stats/')
    .then(response => response.json())
    .then(data => {
        document.getElementById('total-credentials').textContent = data.total_credentials;
        document.getElementById('pending-count').textContent = data.pending_count;
        document.getElementById('verified-count').textContent = data.verified_count;
        document.getElementById('verification-rate').textContent = data.verification_rate + '%';
        document.getElementById('today-verified').textContent = data.today_verified;
        document.getElementById('avg-processing-time').textContent = data.avg_processing_time + 'h';
    })
    .catch(error => {
        console.error('Error refreshing dashboard:', error);
    });
}

// Auto-refresh every 30 seconds
setInterval(refreshDashboard, 30000);

// Close modal when clicking outside
document.getElementById('quickActionModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
{% endblock %}
