{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Verification Queue - {{ site_title|default:"Django Admin" }}{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/credential_admin.css' %}">
    <style>
        .queue-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .filter-group label {
            font-weight: bold;
            font-size: 0.9em;
        }
        .filter-group select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .queue-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: box-shadow 0.2s;
        }
        .queue-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .queue-item.priority-high {
            border-left: 5px solid #dc3545;
        }
        .queue-item.priority-medium {
            border-left: 5px solid #ffc107;
        }
        .queue-item.priority-low {
            border-left: 5px solid #28a745;
        }
        .credential-info {
            flex: 1;
        }
        .credential-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .credential-meta {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .credential-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .detail-item {
            font-size: 0.85em;
        }
        .detail-label {
            font-weight: bold;
            color: #333;
        }
        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .priority-high-badge {
            background: #f8d7da;
            color: #721c24;
        }
        .priority-medium-badge {
            background: #fff3cd;
            color: #856404;
        }
        .priority-low-badge {
            background: #d4edda;
            color: #155724;
        }
        .ai-confidence {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        .confidence-bar {
            width: 100px;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            transition: width 0.3s;
        }
        .confidence-high { background: #28a745; }
        .confidence-medium { background: #ffc107; }
        .confidence-low { background: #dc3545; }
        .queue-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 120px;
        }
        .btn-queue {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            text-decoration: none;
            text-align: center;
            transition: background-color 0.2s;
        }
        .btn-verify {
            background: #28a745;
            color: white;
        }
        .btn-verify:hover {
            background: #218838;
        }
        .btn-reject {
            background: #dc3545;
            color: white;
        }
        .btn-reject:hover {
            background: #c82333;
        }
        .btn-review {
            background: #007bff;
            color: white;
        }
        .btn-review:hover {
            background: #0056b3;
        }
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
{% endblock %}

{% block content %}
<div class="queue-container">
    <div class="queue-header">
        <h1>Verification Queue</h1>
        <p>Manage and prioritize credential verification workflow</p>
    </div>

    <!-- Filters -->
    <form method="get" class="queue-filters">
        <div class="filter-group">
            <label for="status">Status:</label>
            <select name="status" id="status" onchange="this.form.submit()">
                <option value="all" {% if current_filters.status == 'all' %}selected{% endif %}>All</option>
                <option value="pending" {% if current_filters.status == 'pending' %}selected{% endif %}>Pending</option>
                <option value="verified" {% if current_filters.status == 'verified' %}selected{% endif %}>Verified</option>
                <option value="invalid" {% if current_filters.status == 'invalid' %}selected{% endif %}>Invalid</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="credential_type">Credential Type:</label>
            <select name="credential_type" id="credential_type" onchange="this.form.submit()">
                <option value="">All Types</option>
                {% for cred_type in credential_types %}
                <option value="{{ cred_type.id }}" {% if current_filters.credential_type == cred_type.id|stringformat:"s" %}selected{% endif %}>
                    {{ cred_type.name }}
                </option>
                {% endfor %}
            </select>
        </div>

        <div class="filter-group">
            <label for="priority">Priority:</label>
            <select name="priority" id="priority" onchange="this.form.submit()">
                <option value="">All Priorities</option>
                <option value="high" {% if current_filters.priority == 'high' %}selected{% endif %}>High</option>
                <option value="medium" {% if current_filters.priority == 'medium' %}selected{% endif %}>Medium</option>
                <option value="low" {% if current_filters.priority == 'low' %}selected{% endif %}>Low</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="date_range">Date Range:</label>
            <select name="date_range" id="date_range" onchange="this.form.submit()">
                <option value="">All Time</option>
                <option value="today" {% if current_filters.date_range == 'today' %}selected{% endif %}>Today</option>
                <option value="week" {% if current_filters.date_range == 'week' %}selected{% endif %}>This Week</option>
                <option value="month" {% if current_filters.date_range == 'month' %}selected{% endif %}>This Month</option>
            </select>
        </div>

        <div class="filter-group">
            <label>&nbsp;</label>
            <a href="{% url 'admin:credentials_verification_queue' %}" class="btn-queue btn-review">Clear Filters</a>
        </div>
    </form>

    <!-- Queue Items -->
    <div class="queue-list">
        {% for item in page_obj %}
        <div class="queue-item priority-{{ item.priority_label|lower }}">
            <div class="credential-info">
                <div class="credential-title">
                    {{ item.credential.credential_type.name }}
                    <span class="priority-badge priority-{{ item.priority_label|lower }}-badge">
                        {{ item.priority_label }} Priority
                    </span>
                </div>
                
                <div class="credential-meta">
                    <strong>{{ item.credential.user.get_full_name|default:item.credential.user.email }}</strong>
                    • Uploaded {{ item.credential.created_at|timesince }} ago
                    • Status: {{ item.credential.get_status_display }}
                </div>

                <div class="credential-details">
                    <div class="detail-item">
                        <div class="detail-label">Credential Number:</div>
                        {{ item.credential.credential_number|default:"Not provided" }}
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Issuing Authority:</div>
                        {{ item.credential.issuing_authority|default:"Not provided" }}
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">State:</div>
                        {{ item.credential.issuing_state|default:"Not provided" }}
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Expiration:</div>
                        {% if item.credential.expiration_date %}
                            {{ item.credential.expiration_date }}
                            {% if item.credential.is_expired %}
                                <span style="color: #dc3545; font-weight: bold;">(Expired)</span>
                            {% endif %}
                        {% else %}
                            No expiration
                        {% endif %}
                    </div>
                </div>

                {% if item.credential.ai_extracted_data.ai_verification %}
                <div class="ai-confidence">
                    <span style="font-size: 0.9em; font-weight: bold;">AI Confidence:</span>
                    <div class="confidence-bar">
                        {% with confidence=item.credential.ai_extracted_data.ai_verification.overall_confidence %}
                        <div class="confidence-fill {% if confidence >= 0.8 %}confidence-high{% elif confidence >= 0.6 %}confidence-medium{% else %}confidence-low{% endif %}" 
                             style="width: {{ confidence|floatformat:0|add:0 }}%"></div>
                        {% endwith %}
                    </div>
                    <span style="font-size: 0.9em;">
                        {{ item.credential.ai_extracted_data.ai_verification.overall_confidence|floatformat:0 }}%
                    </span>
                </div>
                {% endif %}
            </div>

            <div class="queue-actions">
                {% if item.credential.status == 'pending' %}
                <button class="btn-queue btn-verify" onclick="quickAction('{{ item.credential.id }}', 'verify')">
                    ✓ Verify
                </button>
                <button class="btn-queue btn-reject" onclick="quickAction('{{ item.credential.id }}', 'reject')">
                    ✗ Reject
                </button>
                {% endif %}
                <a href="{% url 'admin:credentials_credential_change' item.credential.id %}" class="btn-queue btn-review">
                    📋 Review
                </a>
            </div>
        </div>
        {% empty %}
        <div class="no-results">
            <h3>No credentials found</h3>
            <p>Try adjusting your filters or check back later.</p>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="pagination-container">
        <div class="pagination">
            {% if page_obj.has_previous %}
                <a href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&laquo; first</a>
                <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">previous</a>
            {% endif %}

            <span class="current">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">next</a>
                <a href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">last &raquo;</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Quick Action Modal (reuse from dashboard) -->
<div id="quickActionModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; min-width: 400px;">
        <h3 id="modalTitle">Verify Credential</h3>
        <form id="quickActionForm">
            <div style="margin-bottom: 15px;">
                <label for="actionNotes">Notes (optional):</label>
                <textarea id="actionNotes" name="notes" rows="3" style="width: 100%; margin-top: 5px;"></textarea>
            </div>
            <div style="text-align: right;">
                <button type="button" onclick="closeModal()" style="margin-right: 10px;">Cancel</button>
                <button type="submit" id="confirmAction" class="button">Confirm</button>
            </div>
        </form>
    </div>
</div>

<script>
let currentCredentialId = null;
let currentAction = null;

function quickAction(credentialId, action) {
    currentCredentialId = credentialId;
    currentAction = action;
    
    const modal = document.getElementById('quickActionModal');
    const title = document.getElementById('modalTitle');
    const confirmBtn = document.getElementById('confirmAction');
    
    switch(action) {
        case 'verify':
            title.textContent = 'Verify Credential';
            confirmBtn.textContent = 'Verify';
            confirmBtn.className = 'button btn-verify';
            break;
        case 'reject':
            title.textContent = 'Reject Credential';
            confirmBtn.textContent = 'Reject';
            confirmBtn.className = 'button btn-reject';
            break;
    }
    
    modal.style.display = 'block';
}

function closeModal() {
    document.getElementById('quickActionModal').style.display = 'none';
    document.getElementById('actionNotes').value = '';
}

document.getElementById('quickActionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const notes = document.getElementById('actionNotes').value;
    const formData = new FormData();
    formData.append('action', currentAction);
    formData.append('notes', notes);
    formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');
    
    fetch(`/admin/credentials/quick-verify/${currentCredentialId}/`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeModal();
            location.reload(); // Refresh the page to show updated status
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
});

// Close modal when clicking outside
document.getElementById('quickActionModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
{% endblock %}
