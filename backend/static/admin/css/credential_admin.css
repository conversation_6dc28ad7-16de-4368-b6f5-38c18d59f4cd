/**
 * Code Med Talent - Credential Admin CSS
 * Enhanced styling for credential verification admin interface.
 */

/* Credential status indicators */
.field-status_display {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

/* Verification confidence styling */
.field-verification_confidence {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    text-align: center;
}

/* Blockchain status styling */
.field-blockchain_status {
    font-weight: bold;
    text-align: center;
}

/* Expiration status styling */
.field-expiration_status {
    font-weight: bold;
    text-align: center;
}

/* Quick actions styling */
.field-verification_actions a {
    padding: 2px 6px;
    border-radius: 3px;
    text-decoration: none;
    font-weight: bold;
    margin: 0 2px;
}

.field-verification_actions a:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

/* AI verification summary styling */
.field-ai_verification_summary {
    max-width: 400px;
}

.field-ai_verification_summary div {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    line-height: 1.4;
}

/* OCR text preview styling */
.field-ocr_text_preview {
    max-width: 500px;
}

.field-ocr_text_preview div {
    max-height: 150px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Verification timeline styling */
.field-verification_timeline div {
    background: #f8f9fa;
    border-left: 4px solid #007cba;
    padding: 10px;
    margin: 5px 0;
}

/* Enhanced filter sidebar */
#changelist-filter {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

#changelist-filter h3 {
    background: #007cba;
    color: white;
    margin: 0;
    padding: 10px;
    font-size: 14px;
}

#changelist-filter ul {
    padding: 10px;
}

#changelist-filter li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

#changelist-filter li:last-child {
    border-bottom: none;
}

/* Stats dashboard */
#credential-stats {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#credential-stats > div {
    flex: 1;
    padding: 10px;
}

#credential-stats > div:not(:last-child) {
    border-right: 1px solid #dee2e6;
}

/* Action buttons enhancement */
.actions {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 20px;
}

.actions select {
    margin-right: 10px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.actions button {
    background: #007cba;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.actions button:hover {
    background: #005a87;
}

/* Table enhancements */
#result_list {
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
}

#result_list th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 12px 8px;
    font-weight: bold;
    text-align: left;
}

#result_list td {
    padding: 10px 8px;
    border-bottom: 1px solid #eee;
    vertical-align: top;
}

#result_list tr:hover {
    background-color: #f5f5f5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #credential-stats {
        flex-direction: column;
    }
    
    #credential-stats > div {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    #credential-stats > div:last-child {
        border-bottom: none;
    }
    
    .field-ai_verification_summary,
    .field-ocr_text_preview {
        max-width: 100%;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: bold;
}

/* Success/error messages */
.messages {
    margin-bottom: 20px;
}

.messages .success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
}

.messages .error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
}

/* Credential type badges */
.credential-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    margin-right: 5px;
}

.credential-type-license {
    background: #e3f2fd;
    color: #1976d2;
}

.credential-type-certification {
    background: #f3e5f5;
    color: #7b1fa2;
}

.credential-type-education {
    background: #e8f5e8;
    color: #388e3c;
}

.credential-type-training {
    background: #fff3e0;
    color: #f57c00;
}

.credential-type-background {
    background: #fce4ec;
    color: #c2185b;
}
