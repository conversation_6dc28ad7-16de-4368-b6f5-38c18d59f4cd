/**
 * Code Med Talent - Credential Admin JavaScript
 * Enhanced admin interface functionality for credential verification.
 */

(function($) {
    'use strict';

    // Quick verification actions
    window.markAsVerified = function(credentialId) {
        if (confirm('Mark this credential as verified?')) {
            // In a real implementation, this would make an AJAX call
            // to update the credential status
            console.log('Marking credential as verified:', credentialId);
            
            // For now, just reload the page
            // In production, you'd implement proper AJAX handling
            alert('Credential verification functionality would be implemented here.');
        }
    };

    window.markAsInvalid = function(credentialId) {
        if (confirm('Mark this credential as invalid/rejected?')) {
            console.log('Marking credential as invalid:', credentialId);
            alert('Credential rejection functionality would be implemented here.');
        }
    };

    // Document ready functions
    $(document).ready(function() {
        
        // Add verification confidence color coding
        $('.field-verification_confidence').each(function() {
            var $this = $(this);
            var text = $this.text().trim();
            var percentage = parseInt(text.replace('%', ''));
            
            if (percentage >= 80) {
                $this.css('background-color', '#d4edda');
            } else if (percentage >= 60) {
                $this.css('background-color', '#fff3cd');
            } else if (percentage > 0) {
                $this.css('background-color', '#f8d7da');
            }
        });

        // Add status indicators
        $('.field-status_display').each(function() {
            var $this = $(this);
            var status = $this.text().toLowerCase();
            
            if (status.includes('verified')) {
                $this.prepend('<span style="color: #28a745;">✓ </span>');
            } else if (status.includes('pending')) {
                $this.prepend('<span style="color: #ffc107;">⏳ </span>');
            } else if (status.includes('invalid') || status.includes('rejected')) {
                $this.prepend('<span style="color: #dc3545;">✗ </span>');
            } else if (status.includes('expired')) {
                $this.prepend('<span style="color: #6c757d;">⏰ </span>');
            }
        });

        // Enhanced filtering
        $('#changelist-filter').on('change', 'select', function() {
            // Auto-submit filter changes
            $(this).closest('form').submit();
        });

        // Bulk verification actions
        $('#action-toggle').change(function() {
            var checked = $(this).is(':checked');
            $('.action-select').prop('checked', checked);
            updateBulkActionButtons();
        });

        $('.action-select').change(function() {
            updateBulkActionButtons();
        });

        function updateBulkActionButtons() {
            var selectedCount = $('.action-select:checked').length;
            var $actionBar = $('.actions');
            
            if (selectedCount > 0) {
                $actionBar.show();
                $actionBar.find('span.action-counter').text(selectedCount + ' of ' + $('.action-select').length + ' selected');
            } else {
                $actionBar.hide();
            }
        }

        // Add quick stats to the top of the page
        addCredentialStats();

        // Add real-time verification status updates
        setupStatusUpdates();
    });

    function addCredentialStats() {
        // This would fetch real stats from the API
        var statsHtml = `
            <div id="credential-stats" style="
                background: #f8f9fa; 
                border: 1px solid #dee2e6; 
                border-radius: 4px; 
                padding: 15px; 
                margin-bottom: 20px;
                display: flex;
                justify-content: space-around;
                text-align: center;
            ">
                <div>
                    <strong style="color: #ffc107;">Pending Review</strong><br>
                    <span id="pending-count" style="font-size: 24px; font-weight: bold;">-</span>
                </div>
                <div>
                    <strong style="color: #28a745;">Verified</strong><br>
                    <span id="verified-count" style="font-size: 24px; font-weight: bold;">-</span>
                </div>
                <div>
                    <strong style="color: #dc3545;">Invalid</strong><br>
                    <span id="invalid-count" style="font-size: 24px; font-weight: bold;">-</span>
                </div>
                <div>
                    <strong style="color: #6c757d;">Expired</strong><br>
                    <span id="expired-count" style="font-size: 24px; font-weight: bold;">-</span>
                </div>
            </div>
        `;
        
        $('#changelist').prepend(statsHtml);
        
        // Load actual stats
        loadCredentialStats();
    }

    function loadCredentialStats() {
        // In a real implementation, this would fetch from the API
        // For now, we'll count from the visible table
        var $rows = $('#result_list tbody tr');
        var stats = {
            pending: 0,
            verified: 0,
            invalid: 0,
            expired: 0
        };

        $rows.each(function() {
            var statusText = $(this).find('.field-status_display').text().toLowerCase();
            
            if (statusText.includes('pending')) {
                stats.pending++;
            } else if (statusText.includes('verified')) {
                stats.verified++;
            } else if (statusText.includes('invalid') || statusText.includes('rejected')) {
                stats.invalid++;
            } else if (statusText.includes('expired')) {
                stats.expired++;
            }
        });

        $('#pending-count').text(stats.pending);
        $('#verified-count').text(stats.verified);
        $('#invalid-count').text(stats.invalid);
        $('#expired-count').text(stats.expired);
    }

    function setupStatusUpdates() {
        // Set up periodic status updates for pending verifications
        setInterval(function() {
            // Check for any credentials that might have been updated
            // In a real implementation, this would poll the API for status changes
            console.log('Checking for credential status updates...');
        }, 30000); // Check every 30 seconds
    }

    // Real-time notification system
    function setupNotifications() {
        // Check for new pending credentials every 60 seconds
        setInterval(function() {
            checkForNewCredentials();
        }, 60000);
    }

    function checkForNewCredentials() {
        // In a real implementation, this would use WebSockets or Server-Sent Events
        // For now, we'll use polling
        fetch('/api/credentials/admin/ajax/dashboard-stats/')
            .then(response => response.json())
            .then(data => {
                const currentPending = parseInt($('#pending-count').text()) || 0;
                const newPending = data.pending_count || 0;

                if (newPending > currentPending) {
                    showNotification(`${newPending - currentPending} new credential(s) pending verification`, 'info');
                    updatePendingCount(newPending);
                }
            })
            .catch(error => {
                console.log('Notification check failed:', error);
            });
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = $(`
            <div class="admin-notification notification-${type}" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'info' ? '#d1ecf1' : '#f8d7da'};
                border: 1px solid ${type === 'info' ? '#bee5eb' : '#f5c6cb'};
                color: ${type === 'info' ? '#0c5460' : '#721c24'};
                padding: 15px 20px;
                border-radius: 4px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                z-index: 9999;
                max-width: 300px;
                cursor: pointer;
            ">
                <strong>${type === 'info' ? 'Info' : 'Alert'}:</strong> ${message}
                <div style="float: right; margin-left: 10px;">×</div>
            </div>
        `);

        $('body').append(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 5000);

        // Remove on click
        notification.click(() => {
            notification.fadeOut(() => notification.remove());
        });
    }

    function updatePendingCount(count) {
        $('#pending-count').text(count);

        // Update page title if on admin page
        if (document.title.includes('Django Admin')) {
            document.title = `(${count}) Django Admin`;
        }
    }

    // Enhanced bulk actions
    function setupBulkActions() {
        // Add "Select All Visible" functionality
        if ($('#action-toggle').length) {
            const selectAllVisible = $(`
                <button type="button" id="select-all-visible" style="margin-left: 10px;">
                    Select All Visible
                </button>
            `);

            $('#action-toggle').parent().append(selectAllVisible);

            selectAllVisible.click(function() {
                $('.action-select:visible').prop('checked', true);
                updateBulkActionButtons();
            });
        }

        // Add keyboard shortcuts
        $(document).keydown(function(e) {
            // Ctrl+A to select all visible
            if (e.ctrlKey && e.key === 'a' && $('.action-select').length) {
                e.preventDefault();
                $('.action-select:visible').prop('checked', true);
                updateBulkActionButtons();
            }

            // Escape to deselect all
            if (e.key === 'Escape') {
                $('.action-select').prop('checked', false);
                $('#action-toggle').prop('checked', false);
                updateBulkActionButtons();
            }
        });
    }

    // Enhanced filtering with URL state management
    function setupAdvancedFiltering() {
        // Save filter state to localStorage
        $('#changelist-filter select, #changelist-filter input').change(function() {
            const filterId = $(this).attr('id') || $(this).attr('name');
            const filterValue = $(this).val();
            localStorage.setItem(`admin_filter_${filterId}`, filterValue);
        });

        // Restore filter state
        $('#changelist-filter select, #changelist-filter input').each(function() {
            const filterId = $(this).attr('id') || $(this).attr('name');
            const savedValue = localStorage.getItem(`admin_filter_${filterId}`);
            if (savedValue) {
                $(this).val(savedValue);
            }
        });
    }

    // Progress tracking for bulk operations
    function setupProgressTracking() {
        $('form[action*="admin"]').submit(function(e) {
            const selectedCount = $('.action-select:checked').length;
            const action = $('select[name="action"]').val();

            if (selectedCount > 10 && action) {
                e.preventDefault();

                if (confirm(`This will process ${selectedCount} credentials. Continue?`)) {
                    showProgressModal(selectedCount, action);
                    processBulkAction(this, selectedCount);
                }
            }
        });
    }

    function showProgressModal(total, action) {
        const modal = $(`
            <div id="progress-modal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            ">
                <div style="
                    background: white;
                    padding: 30px;
                    border-radius: 8px;
                    min-width: 400px;
                    text-align: center;
                ">
                    <h3>Processing ${action.replace('_', ' ')}...</h3>
                    <div style="margin: 20px 0;">
                        <div style="
                            width: 100%;
                            height: 20px;
                            background: #e9ecef;
                            border-radius: 10px;
                            overflow: hidden;
                        ">
                            <div id="progress-bar" style="
                                width: 0%;
                                height: 100%;
                                background: #007bff;
                                transition: width 0.3s;
                            "></div>
                        </div>
                    </div>
                    <div id="progress-text">0 of ${total} processed</div>
                </div>
            </div>
        `);

        $('body').append(modal);
    }

    function processBulkAction(form, total) {
        // Simulate progress (in real implementation, this would track actual progress)
        let processed = 0;
        const interval = setInterval(() => {
            processed += Math.random() * 3;
            if (processed >= total) {
                processed = total;
                clearInterval(interval);

                setTimeout(() => {
                    $('#progress-modal').remove();
                    form.submit();
                }, 500);
            }

            const percentage = (processed / total) * 100;
            $('#progress-bar').css('width', percentage + '%');
            $('#progress-text').text(`${Math.floor(processed)} of ${total} processed`);
        }, 100);
    }

    // Initialize all enhancements
    setupNotifications();
    setupBulkActions();
    setupAdvancedFiltering();
    setupProgressTracking();

    // Export functions for global access
    window.CredentialAdmin = {
        markAsVerified: window.markAsVerified,
        markAsInvalid: window.markAsInvalid,
        loadCredentialStats: loadCredentialStats,
        showNotification: showNotification,
        updatePendingCount: updatePendingCount
    };

})(django.jQuery);
