INFO 2025-06-16 20:46:52,607 autoreload 12438 **********18112 Watching for file changes with StatReloader
WARNING 2025-06-16 20:48:23,242 log 12438 123145437630464 Not Found: /favicon.ico
INFO 2025-06-16 21:14:48,673 autoreload 15649 **********18112 Watching for file changes with StatReloader
ERROR 2025-06-16 21:15:12,869 log 15649 123145469198336 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 112, in get_operation
    operation['responses'] = self._get_response_bodies()
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1397, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1453, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1042, in _map_basic_serializer
    for field in serializer.fields.values():
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 374, in fields
    for key, value in self.get_fields().items():
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 1105, in get_fields
    field_class, field_kwargs = self.build_field(
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 1251, in build_field
    return self.build_unknown_field(field_name, model_class)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 1369, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `license_number` is not valid for model `Credential` in `apps.credentials.serializers.CredentialSerializer`.
ERROR 2025-06-16 21:15:13,329 log 15649 123145469198336 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 486, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 112, in get_operation
    operation['responses'] = self._get_response_bodies()
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1397, in _get_response_bodies
    return {'200': self._get_response_for_code(response_serializers, '200', direction=direction)}
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1453, in _get_response_for_code
    component = self.resolve_serializer(serializer, direction)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1648, in resolve_serializer
    component.schema = self._map_serializer(serializer, direction, bypass_extensions)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 949, in _map_serializer
    schema = self._map_basic_serializer(serializer, direction)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/drf_spectacular/openapi.py", line 1042, in _map_basic_serializer
    for field in serializer.fields.values():
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 374, in fields
    for key, value in self.get_fields().items():
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 1105, in get_fields
    field_class, field_kwargs = self.build_field(
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 1251, in build_field
    return self.build_unknown_field(field_name, model_class)
  File "/Users/<USER>/Documents/augment-projects/code-med-talent/backend/venv/lib/python3.9/site-packages/rest_framework/serializers.py", line 1369, in build_unknown_field
    raise ImproperlyConfigured(
django.core.exceptions.ImproperlyConfigured: Field name `license_number` is not valid for model `Credential` in `apps.credentials.serializers.CredentialSerializer`.
INFO 2025-06-16 22:48:32,692 autoreload 15649 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:48:34,719 autoreload 16738 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:48:53,406 autoreload 16738 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:48:55,117 autoreload 16744 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:49:05,563 autoreload 16744 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/urls.py changed, reloading.
INFO 2025-06-16 22:49:07,080 autoreload 16751 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:49:19,348 autoreload 16751 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/urls.py changed, reloading.
INFO 2025-06-16 22:49:20,904 autoreload 16756 **********18112 Watching for file changes with StatReloader
WARNING 2025-06-16 22:50:35,342 log 16756 123145342857216 Not Found: /
INFO 2025-06-16 22:50:59,990 autoreload 16756 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:51:04,552 autoreload 16780 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:51:14,076 autoreload 16780 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:51:16,098 autoreload 16792 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:51:31,138 autoreload 16792 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:51:33,345 autoreload 16804 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:51:54,922 autoreload 16804 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:51:57,783 autoreload 16812 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:52:09,788 autoreload 16812 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/urls.py changed, reloading.
INFO 2025-06-16 22:52:11,769 autoreload 16820 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:52:23,292 autoreload 16820 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/urls.py changed, reloading.
INFO 2025-06-16 22:52:24,971 autoreload 16826 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:53:47,042 autoreload 16826 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/views.py changed, reloading.
INFO 2025-06-16 22:53:52,540 autoreload 16856 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 22:56:21,156 autoreload 16856 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/admin.py changed, reloading.
INFO 2025-06-16 22:56:23,229 autoreload 16912 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 23:10:53,910 autoreload 16912 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/admin.py changed, reloading.
INFO 2025-06-16 23:10:58,259 autoreload 17100 **********18112 Watching for file changes with StatReloader
INFO 2025-06-16 23:11:12,310 autoreload 17100 **********18112 /Users/<USER>/Documents/augment-projects/code-med-talent/backend/apps/credentials/admin.py changed, reloading.
INFO 2025-06-16 23:11:14,648 autoreload 17110 **********18112 Watching for file changes with StatReloader
