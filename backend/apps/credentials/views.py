"""
Code Med Talent - Credentials API Views
API endpoints for CredentialChain system.
"""

import logging
from typing import Dict, Any

from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .models import CredentialType, Credential
from .serializers import (
    CredentialTypeSerializer,
    CredentialSerializer,
    CredentialUploadSerializer,
    CredentialVerificationSerializer
)
from .services import OCRService, CredentialExtractor
from .verification_services import PrimarySourceVerificationService
from .ai_verification import AIVerificationEngine
from .blockchain_service import blockchain_service
from .compliance import compliance_service

logger = logging.getLogger(__name__)


class CredentialTypeViewSet(ModelViewSet):
    """
    ViewSet for managing credential types.
    """
    queryset = CredentialType.objects.all()
    serializer_class = CredentialTypeSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
        return queryset


class CredentialViewSet(ModelViewSet):
    """
    ViewSet for managing user credentials.
    """
    serializer_class = CredentialSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Users can only see their own credentials
        return Credential.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class CredentialUploadView(APIView):
    """
    API endpoint for uploading and processing credential documents.
    """
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    @extend_schema(
        request=CredentialUploadSerializer,
        responses={
            200: CredentialSerializer,
            400: OpenApiTypes.OBJECT,
        },
        description="Upload and process a credential document using OCR and AI extraction"
    )
    def post(self, request):
        """
        Upload and process a credential document.

        This endpoint:
        1. Accepts uploaded document (PDF, JPG, PNG)
        2. Processes it with OCR to extract text
        3. Uses AI to extract structured credential data
        4. Creates a new Credential record
        5. Returns the processed credential information
        """
        serializer = CredentialUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # Get validated data
                document_file = serializer.validated_data['document_file']
                credential_type_id = serializer.validated_data.get('credential_type_id')
                use_google_vision = serializer.validated_data.get('use_google_vision', True)

                # Get credential type if provided
                credential_type = None
                if credential_type_id:
                    credential_type = get_object_or_404(CredentialType, id=credential_type_id)

                # Process document with OCR
                ocr_service = OCRService()
                ocr_result = ocr_service.process_document(document_file, use_google_vision)

                if ocr_result.error_message:
                    return Response(
                        {'error': f'OCR processing failed: {ocr_result.error_message}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Extract credential data using AI
                extractor = CredentialExtractor()
                credential_type_hint = credential_type.name if credential_type else None
                extracted_data = extractor.extract_credential_data(ocr_result, credential_type_hint)

                # Perform AI verification analysis
                ai_engine = AIVerificationEngine()
                ai_verification = ai_engine.analyze_credential(
                    ocr_result.__dict__,
                    extracted_data.__dict__,
                    None,  # No primary verification yet
                    credential_type_hint
                )

                # Determine credential type if not provided
                if not credential_type and extracted_data.credential_type:
                    try:
                        credential_type = CredentialType.objects.get(
                            name__icontains=extracted_data.credential_type
                        )
                    except CredentialType.DoesNotExist:
                        # Create new credential type if not found
                        credential_type = CredentialType.objects.create(
                            name=extracted_data.credential_type,
                            category=CredentialType.Category.CERTIFICATION,
                            description=f"Auto-created from document upload"
                        )

                # Create credential record
                credential_data = {
                    'user': request.user,
                    'credential_type': credential_type,
                    'document_file': document_file,
                    'license_number': extracted_data.license_number or '',
                    'holder_name': extracted_data.holder_name or '',
                    'issuing_authority': extracted_data.issuing_authority or '',
                    'status': Credential.Status.PENDING,
                    'ai_extracted_data': {
                        'ocr_text': ocr_result.text,
                        'ocr_confidence': ocr_result.confidence,
                        'ocr_method': ocr_result.processing_method,
                        'extraction_confidence': extracted_data.confidence_score,
                        'extracted_fields': extracted_data.extracted_fields or {},
                        'ai_verification': {
                            'overall_confidence': ai_verification.confidence_score,
                            'is_likely_valid': ai_verification.is_likely_valid,
                            'verification_scores': {
                                'ocr_quality': ai_verification.verification_scores.ocr_quality_score,
                                'data_consistency': ai_verification.verification_scores.data_consistency_score,
                                'format_compliance': ai_verification.verification_scores.format_compliance_score,
                                'ai_analysis': ai_verification.verification_scores.ai_analysis_score
                            },
                            'anomalies_detected': ai_verification.anomalies_detected,
                            'risk_factors': ai_verification.verification_scores.risk_factors,
                            'recommendations': ai_verification.verification_scores.recommendations,
                            'verification_recommendation': ai_verification.verification_recommendation,
                            'extracted_insights': ai_verification.extracted_insights
                        }
                    }
                }

                # Parse dates if available
                if extracted_data.issue_date:
                    try:
                        from datetime import datetime
                        credential_data['issue_date'] = datetime.strptime(
                            extracted_data.issue_date, '%m/%d/%Y'
                        ).date()
                    except ValueError:
                        pass

                if extracted_data.expiration_date:
                    try:
                        from datetime import datetime
                        credential_data['expiration_date'] = datetime.strptime(
                            extracted_data.expiration_date, '%m/%d/%Y'
                        ).date()
                    except ValueError:
                        pass

                credential = Credential.objects.create(**credential_data)

                # Return serialized credential
                response_serializer = CredentialSerializer(credential)
                return Response(response_serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Credential upload processing failed: {e}")
            return Response(
                {'error': 'Failed to process credential document'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CredentialVerificationView(APIView):
    """
    API endpoint for manual credential verification by admins.
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=CredentialVerificationSerializer,
        responses={
            200: CredentialSerializer,
            400: OpenApiTypes.OBJECT,
            403: OpenApiTypes.OBJECT,
            404: OpenApiTypes.OBJECT,
        },
        description="Manually verify or reject a credential (admin only)"
    )
    def post(self, request, credential_id):
        """
        Manually verify or reject a credential.
        Only admins can perform verification.
        """
        # Check admin permissions
        if not request.user.is_staff:
            return Response(
                {'error': 'Only administrators can verify credentials'},
                status=status.HTTP_403_FORBIDDEN
            )

        credential = get_object_or_404(Credential, id=credential_id)
        serializer = CredentialVerificationSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Update credential status
            verification_data = serializer.validated_data
            credential.status = verification_data['status']
            credential.verification_notes = verification_data.get('verification_notes', '')
            credential.verification_source = verification_data.get('verification_source', 'Manual Review')
            credential.verification_date = timezone.now()
            credential.verified_by = request.user

            credential.save()

            # TODO: Trigger blockchain hash creation for verified credentials
            # TODO: Send notification to credential owner

            response_serializer = CredentialSerializer(credential)
            return Response(response_serializer.data)

        except Exception as e:
            logger.error(f"Credential verification failed: {e}")
            return Response(
                {'error': 'Failed to update credential verification'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def credential_stats(request):
    """
    Get credential statistics for the current user.
    """
    user_credentials = Credential.objects.filter(user=request.user)

    stats = {
        'total_credentials': user_credentials.count(),
        'verified_credentials': user_credentials.filter(status=Credential.Status.VERIFIED).count(),
        'pending_credentials': user_credentials.filter(status=Credential.Status.PENDING).count(),
        'expired_credentials': user_credentials.filter(status=Credential.Status.EXPIRED).count(),
        'by_category': {}
    }

    # Group by credential type category
    for category in CredentialType.Category.choices:
        category_count = user_credentials.filter(
            credential_type__category=category[0]
        ).count()
        if category_count > 0:
            stats['by_category'][category[1]] = category_count

    return Response(stats)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def reprocess_credential(request, credential_id):
    """
    Reprocess a credential document with updated OCR/AI.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)

    if not credential.document_file:
        return Response(
            {'error': 'No document file available for reprocessing'},
            status=status.HTTP_400_BAD_REQUEST
        )

    try:
        # Reprocess with OCR
        ocr_service = OCRService()
        ocr_result = ocr_service.process_document(credential.document_file, use_google_vision=True)

        # Extract data with AI
        extractor = CredentialExtractor()
        extracted_data = extractor.extract_credential_data(
            ocr_result,
            credential.credential_type.name if credential.credential_type else None
        )

        # Update credential with new data
        credential.ai_extracted_data = {
            'ocr_text': ocr_result.text,
            'ocr_confidence': ocr_result.confidence,
            'ocr_method': ocr_result.processing_method,
            'extraction_confidence': extracted_data.confidence_score,
            'extracted_fields': extracted_data.extracted_fields or {},
            'reprocessed_at': timezone.now().isoformat()
        }

        # Update fields if extraction confidence is high
        if extracted_data.confidence_score > 0.7:
            if extracted_data.license_number:
                credential.license_number = extracted_data.license_number
            if extracted_data.holder_name:
                credential.holder_name = extracted_data.holder_name
            if extracted_data.issuing_authority:
                credential.issuing_authority = extracted_data.issuing_authority

        credential.save()

        serializer = CredentialSerializer(credential)
        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Credential reprocessing failed: {e}")
        return Response(
            {'error': 'Failed to reprocess credential'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def verify_credential_primary_source(request, credential_id):
    """
    Trigger primary source verification for a credential.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)

    try:
        # Prepare verification data
        verification_data = {
            'license_number': credential.credential_number,
            'holder_name': credential.holder_name or request.user.get_full_name(),
            'state': credential.issuing_state,
            'institution': credential.issuing_authority,
            'certification_number': credential.credential_number,
        }

        # Perform primary source verification
        verification_service = PrimarySourceVerificationService()
        result = verification_service.verify_credential(
            credential.credential_type.name,
            verification_data
        )

        # Update credential based on verification result
        if result.is_verified:
            credential.status = Credential.Status.VERIFIED

            # Create blockchain hash for verified credentials
            try:
                credential_data = {
                    'id': str(credential.id),
                    'credential_type': credential.credential_type.name,
                    'credential_number': credential.credential_number,
                    'holder_name': credential.holder_name,
                    'holder_id': str(credential.user.id),
                    'issuing_authority': credential.issuing_authority,
                    'verification_status': 'verified',
                    'verification_date': result.verification_date.isoformat(),
                    'verification_confidence': result.confidence_score
                }

                # Create blockchain hash
                blockchain_hash = blockchain_service.create_credential_hash(credential_data)

                # Store on blockchain
                blockchain_record = blockchain_service.store_credential_on_blockchain(
                    credential_data, blockchain_hash
                )

                if blockchain_record:
                    credential.blockchain_hash = blockchain_hash
                    logger.info(f"Credential {credential.id} stored on blockchain: {blockchain_record.file_id}")

            except Exception as e:
                logger.error(f"Failed to create blockchain record: {e}")

        elif result.status == 'expired':
            credential.status = Credential.Status.EXPIRED
        elif result.status in ['invalid', 'not_found']:
            credential.status = Credential.Status.INVALID
        else:
            credential.status = Credential.Status.PENDING

        credential.verification_date = result.verification_date
        credential.verification_source = result.source
        credential.verification_notes = f"Primary source verification: {result.status}. Details: {result.details}"

        # Store verification details in ai_extracted_data
        if not credential.ai_extracted_data:
            credential.ai_extracted_data = {}

        credential.ai_extracted_data['primary_verification'] = {
            'is_verified': result.is_verified,
            'status': result.status,
            'source': result.source,
            'confidence_score': result.confidence_score,
            'verification_date': result.verification_date.isoformat(),
            'details': result.details,
            'error_message': result.error_message
        }

        credential.save()

        # Return verification result
        response_data = {
            'credential_id': str(credential.id),
            'verification_result': {
                'is_verified': result.is_verified,
                'status': result.status,
                'source': result.source,
                'confidence_score': result.confidence_score,
                'details': result.details
            },
            'credential_status': credential.status,
            'message': f"Primary source verification completed. Status: {result.status}"
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Primary source verification failed: {e}")
        return Response(
            {'error': 'Failed to perform primary source verification'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def run_ai_verification(request, credential_id):
    """
    Run AI verification analysis on a credential.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)

    try:
        # Get existing OCR and extraction data
        ai_data = credential.ai_extracted_data or {}
        ocr_result = {
            'text': ai_data.get('ocr_text', ''),
            'confidence': ai_data.get('ocr_confidence', 0.0),
            'processing_method': ai_data.get('ocr_method', 'unknown')
        }

        extracted_data = ai_data.get('extracted_fields', {})
        if not extracted_data:
            # Fallback to credential fields
            extracted_data = {
                'holder_name': credential.holder_name,
                'license_number': credential.credential_number,
                'issuing_authority': credential.issuing_authority,
                'state': credential.issuing_state,
                'issue_date': str(credential.issue_date) if credential.issue_date else None,
                'expiration_date': str(credential.expiration_date) if credential.expiration_date else None
            }

        # Get primary verification data if available
        primary_verification = ai_data.get('primary_verification')

        # Run AI verification
        ai_engine = AIVerificationEngine()
        ai_result = ai_engine.analyze_credential(
            ocr_result,
            extracted_data,
            primary_verification,
            credential.credential_type.name
        )

        # Update credential with AI verification results
        if not credential.ai_extracted_data:
            credential.ai_extracted_data = {}

        credential.ai_extracted_data['ai_verification'] = {
            'overall_confidence': ai_result.confidence_score,
            'is_likely_valid': ai_result.is_likely_valid,
            'verification_scores': {
                'ocr_quality': ai_result.verification_scores.ocr_quality_score,
                'data_consistency': ai_result.verification_scores.data_consistency_score,
                'format_compliance': ai_result.verification_scores.format_compliance_score,
                'cross_reference': ai_result.verification_scores.cross_reference_score,
                'ai_analysis': ai_result.verification_scores.ai_analysis_score,
                'overall_confidence': ai_result.verification_scores.overall_confidence
            },
            'anomalies_detected': ai_result.anomalies_detected,
            'risk_factors': ai_result.verification_scores.risk_factors,
            'recommendations': ai_result.verification_scores.recommendations,
            'verification_recommendation': ai_result.verification_recommendation,
            'extracted_insights': ai_result.extracted_insights,
            'processing_notes': ai_result.processing_notes,
            'analysis_date': timezone.now().isoformat()
        }

        credential.save()

        # Return AI verification results
        response_data = {
            'credential_id': str(credential.id),
            'ai_verification_result': {
                'overall_confidence': ai_result.confidence_score,
                'is_likely_valid': ai_result.is_likely_valid,
                'verification_recommendation': ai_result.verification_recommendation,
                'verification_scores': {
                    'ocr_quality': ai_result.verification_scores.ocr_quality_score,
                    'data_consistency': ai_result.verification_scores.data_consistency_score,
                    'format_compliance': ai_result.verification_scores.format_compliance_score,
                    'cross_reference': ai_result.verification_scores.cross_reference_score,
                    'ai_analysis': ai_result.verification_scores.ai_analysis_score
                },
                'anomalies_detected': ai_result.anomalies_detected,
                'risk_factors': ai_result.verification_scores.risk_factors,
                'recommendations': ai_result.verification_scores.recommendations,
                'extracted_insights': ai_result.extracted_insights
            },
            'message': f"AI verification completed. Recommendation: {ai_result.verification_recommendation}"
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"AI verification failed: {e}")
        return Response(
            {'error': 'Failed to run AI verification'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_career_passport(request):
    """
    Create a Career Passport for the authenticated user.
    """
    try:
        # Get all verified credentials for the user
        verified_credentials = Credential.objects.filter(
            user=request.user,
            status=Credential.Status.VERIFIED
        )

        if not verified_credentials.exists():
            return Response(
                {'error': 'No verified credentials found. Please verify at least one credential first.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare credential data for passport
        credential_data_list = []
        for credential in verified_credentials:
            credential_data = {
                'id': str(credential.id),
                'credential_type': credential.credential_type.name,
                'credential_number': credential.credential_number,
                'holder_name': credential.holder_name or request.user.get_full_name(),
                'holder_id': str(request.user.id),
                'issuing_authority': credential.issuing_authority,
                'verification_status': credential.status,
                'verification_date': credential.verification_date.isoformat() if credential.verification_date else None,
                'expiration_date': credential.expiration_date.isoformat() if credential.expiration_date else None,
                'verification_confidence': credential.ai_extracted_data.get('ai_verification', {}).get('overall_confidence', 0.8) if credential.ai_extracted_data else 0.8
            }
            credential_data_list.append(credential_data)

        # Create Career Passport
        passport = blockchain_service.create_career_passport(
            str(request.user.id),
            credential_data_list
        )

        # Generate verification report
        verification_report = blockchain_service.get_passport_verification_report(passport)

        response_data = {
            'career_passport': {
                'passport_id': passport.passport_id,
                'owner_id': passport.owner_id,
                'total_credentials': passport.total_credentials,
                'verification_score': passport.verification_score,
                'created_at': passport.created_at.isoformat(),
                'blockchain_records_count': len(passport.blockchain_records)
            },
            'verification_report': verification_report,
            'message': f"Career Passport created successfully with {passport.total_credentials} verified credentials"
        }

        return Response(response_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Failed to create Career Passport: {e}")
        return Response(
            {'error': 'Failed to create Career Passport'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_career_passport_report(request):
    """
    Get Career Passport verification report for the authenticated user.
    """
    try:
        # Get all verified credentials for the user
        verified_credentials = Credential.objects.filter(
            user=request.user,
            status=Credential.Status.VERIFIED
        )

        if not verified_credentials.exists():
            return Response(
                {'message': 'No verified credentials found. Create some verified credentials first.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create a temporary passport for report generation
        credential_data_list = []
        for credential in verified_credentials:
            credential_data = {
                'id': str(credential.id),
                'credential_type': credential.credential_type.name,
                'credential_number': credential.credential_number,
                'holder_name': credential.holder_name or request.user.get_full_name(),
                'holder_id': str(request.user.id),
                'issuing_authority': credential.issuing_authority,
                'verification_status': credential.status,
                'verification_date': credential.verification_date.isoformat() if credential.verification_date else None,
                'expiration_date': credential.expiration_date.isoformat() if credential.expiration_date else None,
                'verification_confidence': credential.ai_extracted_data.get('ai_verification', {}).get('overall_confidence', 0.8) if credential.ai_extracted_data else 0.8,
                'blockchain_hash': credential.blockchain_hash or '',
                'blockchain_file_id': f"file_{credential.id}" if credential.blockchain_hash else ''
            }
            credential_data_list.append(credential_data)

        # Create temporary passport
        passport = blockchain_service.create_career_passport(
            str(request.user.id),
            credential_data_list
        )

        # Generate comprehensive report
        verification_report = blockchain_service.get_passport_verification_report(passport)

        # Add additional user information
        verification_report['user_info'] = {
            'user_id': str(request.user.id),
            'full_name': request.user.get_full_name(),
            'email': request.user.email,
            'date_joined': request.user.date_joined.isoformat()
        }

        return Response(verification_report)

    except Exception as e:
        logger.error(f"Failed to generate Career Passport report: {e}")
        return Response(
            {'error': 'Failed to generate Career Passport report'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def verify_blockchain_credential(request, credential_id):
    """
    Verify a credential's blockchain integrity.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)

    try:
        if not credential.blockchain_hash:
            return Response(
                {'error': 'Credential does not have a blockchain hash'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare credential data for verification
        credential_data = {
            'id': str(credential.id),
            'credential_type': credential.credential_type.name,
            'credential_number': credential.credential_number,
            'holder_name': credential.holder_name,
            'holder_id': str(credential.user.id),
            'issuing_authority': credential.issuing_authority,
            'verification_status': credential.status,
            'verification_date': credential.verification_date.isoformat() if credential.verification_date else None
        }

        # Create hash of current data
        current_hash = blockchain_service.create_credential_hash(credential_data)

        # Compare with stored hash
        hash_matches = current_hash == credential.blockchain_hash

        # Mock blockchain verification (in production, this would query Hedera)
        blockchain_verified = True  # Mock verification always passes

        response_data = {
            'credential_id': str(credential.id),
            'blockchain_verification': {
                'hash_matches': hash_matches,
                'blockchain_verified': blockchain_verified,
                'stored_hash': credential.blockchain_hash,
                'current_hash': current_hash,
                'verification_date': timezone.now().isoformat(),
                'integrity_status': 'VERIFIED' if hash_matches and blockchain_verified else 'COMPROMISED'
            },
            'message': 'Blockchain verification completed'
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Blockchain verification failed: {e}")
        return Response(
            {'error': 'Failed to verify blockchain credential'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAdminUser])
def compliance_report(request):
    """
    Generate HIPAA compliance report (admin only).
    """
    try:
        from datetime import datetime, timedelta

        # Get date range from query parameters
        days = int(request.GET.get('days', 30))
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        # Generate compliance report
        report = compliance_service.generate_compliance_report(start_date, end_date)

        return Response(report)

    except Exception as e:
        logger.error(f"Failed to generate compliance report: {e}")
        return Response(
            {'error': 'Failed to generate compliance report'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAdminUser])
def data_retention_check(request):
    """
    Run data retention compliance check (admin only).
    """
    try:
        result = compliance_service.check_data_retention_compliance()
        return Response(result)

    except Exception as e:
        logger.error(f"Data retention check failed: {e}")
        return Response(
            {'error': 'Data retention check failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
