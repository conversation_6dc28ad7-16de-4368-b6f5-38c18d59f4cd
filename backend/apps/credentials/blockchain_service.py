"""
Code Med Talent - Blockchain Career Passport Service
Hedera Hashgraph integration for tamper-proof credential verification.
"""

import logging
import hashlib
import json
from typing import Dict, Optional, Any, List
from dataclasses import dataclass
from datetime import datetime

from django.conf import settings
from django.utils import timezone

# Hedera SDK imports
try:
    from hedera import (
        Client, 
        PrivateKey, 
        AccountId,
        FileCreateTransaction,
        FileAppendTransaction,
        FileContentsQuery,
        Hbar,
        TransactionId
    )
    HEDERA_AVAILABLE = True
except ImportError:
    HEDERA_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class BlockchainRecord:
    """Blockchain record for credential."""
    transaction_id: str
    file_id: str
    hash_value: str
    timestamp: datetime
    network: str
    status: str
    cost: Optional[float] = None


@dataclass
class CareerPassport:
    """Digital Career Passport containing all verified credentials."""
    passport_id: str
    owner_id: str
    credentials: List[Dict[str, Any]]
    blockchain_records: List[BlockchainRecord]
    created_at: datetime
    updated_at: datetime
    verification_score: float
    total_credentials: int


class HederaBlockchainService:
    """
    Service for interacting with Hedera Hashgraph network.
    Creates tamper-proof records of credential verifications.
    """
    
    def __init__(self):
        self.client = None
        self.operator_id = None
        self.operator_key = None
        
        if HEDERA_AVAILABLE and self._has_hedera_config():
            try:
                self._initialize_hedera_client()
            except Exception as e:
                logger.error(f"Failed to initialize Hedera client: {e}")

    def _has_hedera_config(self) -> bool:
        """Check if Hedera configuration is available."""
        return (
            hasattr(settings, 'HEDERA_ACCOUNT_ID') and
            hasattr(settings, 'HEDERA_PRIVATE_KEY') and
            hasattr(settings, 'HEDERA_NETWORK')
        )

    def _initialize_hedera_client(self):
        """Initialize Hedera client with credentials."""
        try:
            # Parse account ID and private key
            self.operator_id = AccountId.fromString(settings.HEDERA_ACCOUNT_ID)
            self.operator_key = PrivateKey.fromString(settings.HEDERA_PRIVATE_KEY)
            
            # Create client for specified network
            if settings.HEDERA_NETWORK.lower() == 'testnet':
                self.client = Client.forTestnet()
            elif settings.HEDERA_NETWORK.lower() == 'mainnet':
                self.client = Client.forMainnet()
            else:
                self.client = Client.forPreviewnet()
            
            # Set operator
            self.client.setOperator(self.operator_id, self.operator_key)
            
            logger.info(f"Hedera client initialized for {settings.HEDERA_NETWORK}")
            
        except Exception as e:
            logger.error(f"Hedera client initialization failed: {e}")
            self.client = None

    def create_credential_hash(self, credential_data: Dict[str, Any]) -> str:
        """
        Create a cryptographic hash of credential data.
        """
        try:
            # Normalize credential data for consistent hashing
            normalized_data = self._normalize_credential_data(credential_data)
            
            # Create JSON string with sorted keys
            json_string = json.dumps(normalized_data, sort_keys=True, separators=(',', ':'))
            
            # Create SHA-256 hash
            hash_object = hashlib.sha256(json_string.encode('utf-8'))
            return hash_object.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to create credential hash: {e}")
            return ""

    def store_credential_on_blockchain(self, credential_data: Dict[str, Any], 
                                     credential_hash: str) -> Optional[BlockchainRecord]:
        """
        Store credential hash on Hedera Hashgraph.
        """
        if not self.client:
            logger.warning("Hedera client not available, creating mock blockchain record")
            return self._create_mock_blockchain_record(credential_hash)
        
        try:
            # Prepare data for blockchain storage
            blockchain_data = {
                'credential_hash': credential_hash,
                'credential_type': credential_data.get('credential_type', ''),
                'holder_id': credential_data.get('holder_id', ''),
                'verification_status': credential_data.get('verification_status', ''),
                'timestamp': timezone.now().isoformat(),
                'issuer': 'Code-Med-Talent-Platform'
            }
            
            # Convert to JSON
            data_json = json.dumps(blockchain_data, separators=(',', ':'))
            data_bytes = data_json.encode('utf-8')
            
            # Create file on Hedera
            file_create_tx = (
                FileCreateTransaction()
                .setContents(data_bytes)
                .setKeys([self.operator_key.getPublicKey()])
                .setMaxTransactionFee(Hbar(2))
            )
            
            # Execute transaction
            file_create_response = file_create_tx.execute(self.client)
            file_create_receipt = file_create_response.getReceipt(self.client)
            file_id = file_create_receipt.fileId
            
            # Create blockchain record
            record = BlockchainRecord(
                transaction_id=str(file_create_response.transactionId),
                file_id=str(file_id),
                hash_value=credential_hash,
                timestamp=timezone.now(),
                network=settings.HEDERA_NETWORK,
                status='confirmed',
                cost=float(file_create_tx.getMaxTransactionFee().toTinybars()) / 100000000
            )
            
            logger.info(f"Credential stored on Hedera: {file_id}")
            return record
            
        except Exception as e:
            logger.error(f"Failed to store credential on blockchain: {e}")
            return self._create_mock_blockchain_record(credential_hash)

    def verify_credential_on_blockchain(self, file_id: str, 
                                      expected_hash: str) -> bool:
        """
        Verify credential integrity using blockchain record.
        """
        if not self.client:
            logger.warning("Hedera client not available, using mock verification")
            return True  # Mock verification always passes
        
        try:
            # Query file contents from Hedera
            file_query = FileContentsQuery().setFileId(file_id)
            file_contents = file_query.execute(self.client)
            
            # Parse stored data
            stored_data = json.loads(file_contents.decode('utf-8'))
            stored_hash = stored_data.get('credential_hash', '')
            
            # Verify hash matches
            return stored_hash == expected_hash
            
        except Exception as e:
            logger.error(f"Failed to verify credential on blockchain: {e}")
            return False

    def create_career_passport(self, user_id: str, 
                             verified_credentials: List[Dict[str, Any]]) -> CareerPassport:
        """
        Create a comprehensive Career Passport for a user.
        """
        try:
            # Generate passport ID
            passport_id = self._generate_passport_id(user_id)
            
            # Process credentials for passport
            passport_credentials = []
            blockchain_records = []
            total_verification_score = 0.0
            
            for credential in verified_credentials:
                # Create credential hash
                cred_hash = self.create_credential_hash(credential)
                
                # Store on blockchain
                blockchain_record = self.store_credential_on_blockchain(credential, cred_hash)
                if blockchain_record:
                    blockchain_records.append(blockchain_record)
                
                # Add to passport
                passport_credential = {
                    'credential_id': credential.get('id', ''),
                    'credential_type': credential.get('credential_type', ''),
                    'credential_number': credential.get('credential_number', ''),
                    'issuing_authority': credential.get('issuing_authority', ''),
                    'verification_status': credential.get('verification_status', ''),
                    'verification_date': credential.get('verification_date', ''),
                    'expiration_date': credential.get('expiration_date', ''),
                    'blockchain_hash': cred_hash,
                    'blockchain_file_id': blockchain_record.file_id if blockchain_record else '',
                    'verification_confidence': credential.get('verification_confidence', 0.0)
                }
                passport_credentials.append(passport_credential)
                
                # Add to total verification score
                total_verification_score += credential.get('verification_confidence', 0.0)
            
            # Calculate average verification score
            avg_verification_score = (
                total_verification_score / len(verified_credentials) 
                if verified_credentials else 0.0
            )
            
            # Create Career Passport
            passport = CareerPassport(
                passport_id=passport_id,
                owner_id=user_id,
                credentials=passport_credentials,
                blockchain_records=blockchain_records,
                created_at=timezone.now(),
                updated_at=timezone.now(),
                verification_score=avg_verification_score,
                total_credentials=len(verified_credentials)
            )
            
            logger.info(f"Career Passport created for user {user_id}: {passport_id}")
            return passport
            
        except Exception as e:
            logger.error(f"Failed to create Career Passport: {e}")
            raise

    def update_career_passport(self, passport: CareerPassport, 
                             new_credential: Dict[str, Any]) -> CareerPassport:
        """
        Update an existing Career Passport with a new credential.
        """
        try:
            # Create hash for new credential
            cred_hash = self.create_credential_hash(new_credential)
            
            # Store on blockchain
            blockchain_record = self.store_credential_on_blockchain(new_credential, cred_hash)
            if blockchain_record:
                passport.blockchain_records.append(blockchain_record)
            
            # Add credential to passport
            passport_credential = {
                'credential_id': new_credential.get('id', ''),
                'credential_type': new_credential.get('credential_type', ''),
                'credential_number': new_credential.get('credential_number', ''),
                'issuing_authority': new_credential.get('issuing_authority', ''),
                'verification_status': new_credential.get('verification_status', ''),
                'verification_date': new_credential.get('verification_date', ''),
                'expiration_date': new_credential.get('expiration_date', ''),
                'blockchain_hash': cred_hash,
                'blockchain_file_id': blockchain_record.file_id if blockchain_record else '',
                'verification_confidence': new_credential.get('verification_confidence', 0.0)
            }
            passport.credentials.append(passport_credential)
            
            # Update passport metadata
            passport.total_credentials = len(passport.credentials)
            passport.updated_at = timezone.now()
            
            # Recalculate verification score
            total_score = sum(cred.get('verification_confidence', 0.0) for cred in passport.credentials)
            passport.verification_score = total_score / passport.total_credentials if passport.total_credentials > 0 else 0.0
            
            logger.info(f"Career Passport updated: {passport.passport_id}")
            return passport
            
        except Exception as e:
            logger.error(f"Failed to update Career Passport: {e}")
            raise

    def verify_career_passport_integrity(self, passport: CareerPassport) -> bool:
        """
        Verify the integrity of all credentials in a Career Passport.
        """
        try:
            verification_results = []
            
            for credential in passport.credentials:
                blockchain_file_id = credential.get('blockchain_file_id', '')
                expected_hash = credential.get('blockchain_hash', '')
                
                if blockchain_file_id and expected_hash:
                    is_valid = self.verify_credential_on_blockchain(blockchain_file_id, expected_hash)
                    verification_results.append(is_valid)
                else:
                    verification_results.append(False)
            
            # All credentials must be valid
            return all(verification_results) if verification_results else False
            
        except Exception as e:
            logger.error(f"Failed to verify Career Passport integrity: {e}")
            return False

    def get_passport_verification_report(self, passport: CareerPassport) -> Dict[str, Any]:
        """
        Generate a comprehensive verification report for a Career Passport.
        """
        try:
            report = {
                'passport_id': passport.passport_id,
                'owner_id': passport.owner_id,
                'total_credentials': passport.total_credentials,
                'overall_verification_score': passport.verification_score,
                'created_at': passport.created_at.isoformat(),
                'last_updated': passport.updated_at.isoformat(),
                'blockchain_network': settings.HEDERA_NETWORK if hasattr(settings, 'HEDERA_NETWORK') else 'mock',
                'credentials': [],
                'integrity_verified': self.verify_career_passport_integrity(passport),
                'verification_summary': {
                    'verified_credentials': 0,
                    'pending_credentials': 0,
                    'expired_credentials': 0,
                    'invalid_credentials': 0
                }
            }
            
            # Process each credential
            for credential in passport.credentials:
                cred_report = {
                    'credential_type': credential.get('credential_type', ''),
                    'credential_number': credential.get('credential_number', ''),
                    'issuing_authority': credential.get('issuing_authority', ''),
                    'verification_status': credential.get('verification_status', ''),
                    'verification_confidence': credential.get('verification_confidence', 0.0),
                    'blockchain_verified': bool(credential.get('blockchain_file_id', '')),
                    'expiration_date': credential.get('expiration_date', ''),
                    'is_expired': self._is_credential_expired(credential.get('expiration_date', ''))
                }
                report['credentials'].append(cred_report)
                
                # Update summary counts
                status = credential.get('verification_status', '').lower()
                if status == 'verified':
                    report['verification_summary']['verified_credentials'] += 1
                elif status == 'pending':
                    report['verification_summary']['pending_credentials'] += 1
                elif status == 'expired':
                    report['verification_summary']['expired_credentials'] += 1
                else:
                    report['verification_summary']['invalid_credentials'] += 1
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate verification report: {e}")
            return {'error': str(e)}

    # Helper methods
    def _normalize_credential_data(self, credential_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize credential data for consistent hashing."""
        normalized = {}
        
        # Include only essential fields for hashing
        essential_fields = [
            'credential_type', 'credential_number', 'holder_name',
            'issuing_authority', 'issue_date', 'expiration_date',
            'verification_status', 'verification_date'
        ]
        
        for field in essential_fields:
            if field in credential_data and credential_data[field] is not None:
                # Normalize strings
                value = credential_data[field]
                if isinstance(value, str):
                    value = value.strip().lower()
                normalized[field] = value
        
        return normalized

    def _generate_passport_id(self, user_id: str) -> str:
        """Generate unique passport ID."""
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        hash_input = f"{user_id}_{timestamp}_career_passport"
        hash_object = hashlib.md5(hash_input.encode('utf-8'))
        return f"CP_{hash_object.hexdigest()[:12].upper()}"

    def _create_mock_blockchain_record(self, credential_hash: str) -> BlockchainRecord:
        """Create mock blockchain record when Hedera is not available."""
        return BlockchainRecord(
            transaction_id=f"mock_tx_{timezone.now().strftime('%Y%m%d%H%M%S')}",
            file_id=f"mock_file_{credential_hash[:8]}",
            hash_value=credential_hash,
            timestamp=timezone.now(),
            network='mock',
            status='mock_confirmed',
            cost=0.0
        )

    def _is_credential_expired(self, expiration_date: str) -> bool:
        """Check if credential is expired."""
        if not expiration_date:
            return False
        
        try:
            from datetime import datetime
            exp_date = datetime.fromisoformat(expiration_date.replace('Z', '+00:00'))
            return exp_date.date() < timezone.now().date()
        except:
            return False


# Singleton instance
blockchain_service = HederaBlockchainService()
