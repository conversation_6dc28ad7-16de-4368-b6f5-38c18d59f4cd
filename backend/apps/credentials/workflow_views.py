"""
Code Med Talent - Verification Workflow Views
Enhanced workflow management for credential verification.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

from django.shortcuts import render, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse
from django.db.models import Count, Q, Avg
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.urls import reverse
from django.core.paginator import Paginator

from .models import Credential, CredentialType
from .compliance import ComplianceAuditLog, compliance_service

logger = logging.getLogger(__name__)


def is_staff_or_admin(user):
    """Check if user is staff or admin."""
    return user.is_staff or user.is_superuser


@login_required
@user_passes_test(is_staff_or_admin)
def verification_dashboard(request):
    """
    Main verification workflow dashboard.
    """
    try:
        # Get dashboard statistics
        stats = _get_dashboard_stats()
        
        # Get pending credentials requiring review
        pending_credentials = Credential.objects.filter(
            status=Credential.Status.PENDING
        ).select_related('user', 'credential_type').order_by('-created_at')[:10]
        
        # Get recent verification activity
        recent_activity = _get_recent_activity()
        
        # Get verification queue metrics
        queue_metrics = _get_queue_metrics()
        
        # Get AI analysis alerts
        ai_alerts = _get_ai_analysis_alerts()
        
        context = {
            'stats': stats,
            'pending_credentials': pending_credentials,
            'recent_activity': recent_activity,
            'queue_metrics': queue_metrics,
            'ai_alerts': ai_alerts,
            'page_title': 'Verification Dashboard'
        }
        
        return render(request, 'admin/credentials/verification_dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Dashboard loading failed: {e}")
        messages.error(request, 'Failed to load dashboard data')
        return render(request, 'admin/credentials/verification_dashboard.html', {
            'error': 'Failed to load dashboard data'
        })


@login_required
@user_passes_test(is_staff_or_admin)
def verification_queue(request):
    """
    Verification queue with filtering and sorting.
    """
    try:
        # Get filter parameters
        status_filter = request.GET.get('status', 'pending')
        credential_type_filter = request.GET.get('credential_type', '')
        priority_filter = request.GET.get('priority', '')
        date_filter = request.GET.get('date_range', '')
        
        # Build queryset
        credentials = Credential.objects.select_related('user', 'credential_type')
        
        # Apply filters
        if status_filter and status_filter != 'all':
            if status_filter == 'pending':
                credentials = credentials.filter(status=Credential.Status.PENDING)
            elif status_filter == 'verified':
                credentials = credentials.filter(status=Credential.Status.VERIFIED)
            elif status_filter == 'invalid':
                credentials = credentials.filter(status=Credential.Status.INVALID)
        
        if credential_type_filter:
            credentials = credentials.filter(credential_type__id=credential_type_filter)
        
        if date_filter:
            if date_filter == 'today':
                credentials = credentials.filter(created_at__date=timezone.now().date())
            elif date_filter == 'week':
                week_ago = timezone.now() - timedelta(days=7)
                credentials = credentials.filter(created_at__gte=week_ago)
            elif date_filter == 'month':
                month_ago = timezone.now() - timedelta(days=30)
                credentials = credentials.filter(created_at__gte=month_ago)
        
        # Add priority scoring
        credentials_with_priority = []
        for credential in credentials:
            priority_score = _calculate_priority_score(credential)
            credentials_with_priority.append({
                'credential': credential,
                'priority_score': priority_score,
                'priority_label': _get_priority_label(priority_score)
            })
        
        # Sort by priority
        if priority_filter == 'high':
            credentials_with_priority = [c for c in credentials_with_priority if c['priority_score'] >= 80]
        elif priority_filter == 'medium':
            credentials_with_priority = [c for c in credentials_with_priority if 50 <= c['priority_score'] < 80]
        elif priority_filter == 'low':
            credentials_with_priority = [c for c in credentials_with_priority if c['priority_score'] < 50]
        
        # Sort by priority score (highest first)
        credentials_with_priority.sort(key=lambda x: x['priority_score'], reverse=True)
        
        # Pagination
        paginator = Paginator(credentials_with_priority, 25)
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)
        
        # Get filter options
        credential_types = CredentialType.objects.filter(is_active=True)
        
        context = {
            'page_obj': page_obj,
            'credential_types': credential_types,
            'current_filters': {
                'status': status_filter,
                'credential_type': credential_type_filter,
                'priority': priority_filter,
                'date_range': date_filter
            },
            'page_title': 'Verification Queue'
        }
        
        return render(request, 'admin/credentials/verification_queue.html', context)
        
    except Exception as e:
        logger.error(f"Verification queue loading failed: {e}")
        messages.error(request, 'Failed to load verification queue')
        return render(request, 'admin/credentials/verification_queue.html', {
            'error': 'Failed to load verification queue'
        })


@login_required
@user_passes_test(is_staff_or_admin)
@require_http_methods(["POST"])
def quick_verify_credential(request, credential_id):
    """
    Quick verification action from dashboard.
    """
    try:
        credential = get_object_or_404(Credential, id=credential_id)
        action = request.POST.get('action')
        notes = request.POST.get('notes', '')
        
        if action == 'verify':
            credential.status = Credential.Status.VERIFIED
            credential.verified_by = request.user
            credential.verification_date = timezone.now()
            credential.verification_notes = f"Quick verified by {request.user.get_full_name()}. {notes}".strip()
            
            # Log compliance activity
            compliance_service.log_access(
                user=request.user,
                action='QUICK_VERIFY_CREDENTIAL',
                resource_type='credential',
                resource_id=str(credential.id),
                request=request,
                details={
                    'credential_type': credential.credential_type.name,
                    'verification_method': 'quick_verify',
                    'notes': notes
                },
                risk_level='high'
            )
            
            messages.success(request, f'Credential {credential.credential_number} verified successfully')
            
        elif action == 'reject':
            credential.status = Credential.Status.INVALID
            credential.verified_by = request.user
            credential.verification_date = timezone.now()
            credential.verification_notes = f"Rejected by {request.user.get_full_name()}. {notes}".strip()
            
            # Log compliance activity
            compliance_service.log_access(
                user=request.user,
                action='QUICK_REJECT_CREDENTIAL',
                resource_type='credential',
                resource_id=str(credential.id),
                request=request,
                details={
                    'credential_type': credential.credential_type.name,
                    'verification_method': 'quick_reject',
                    'notes': notes
                },
                risk_level='high'
            )
            
            messages.warning(request, f'Credential {credential.credential_number} rejected')
            
        elif action == 'flag':
            # Add flag for manual review
            if not credential.verification_notes:
                credential.verification_notes = ''
            credential.verification_notes += f"\n[FLAGGED by {request.user.get_full_name()}]: {notes}"
            
            messages.info(request, f'Credential {credential.credential_number} flagged for review')
        
        credential.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Credential {action}ed successfully',
            'new_status': credential.get_status_display()
        })
        
    except Exception as e:
        logger.error(f"Quick verification failed: {e}")
        return JsonResponse({
            'success': False,
            'error': 'Verification action failed'
        }, status=500)


@login_required
@user_passes_test(is_staff_or_admin)
def verification_analytics(request):
    """
    Verification analytics and reporting.
    """
    try:
        # Get date range
        days = int(request.GET.get('days', 30))
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Get verification statistics
        analytics_data = _get_verification_analytics(start_date, end_date)
        
        # Get team performance
        team_performance = _get_team_performance(start_date, end_date)
        
        # Get compliance metrics
        compliance_metrics = _get_compliance_metrics(start_date, end_date)
        
        context = {
            'analytics_data': analytics_data,
            'team_performance': team_performance,
            'compliance_metrics': compliance_metrics,
            'date_range': {
                'start': start_date.strftime('%Y-%m-%d'),
                'end': end_date.strftime('%Y-%m-%d'),
                'days': days
            },
            'page_title': 'Verification Analytics'
        }
        
        return render(request, 'admin/credentials/verification_analytics.html', context)
        
    except Exception as e:
        logger.error(f"Analytics loading failed: {e}")
        messages.error(request, 'Failed to load analytics data')
        return render(request, 'admin/credentials/verification_analytics.html', {
            'error': 'Failed to load analytics data'
        })


@login_required
@user_passes_test(is_staff_or_admin)
def ajax_dashboard_stats(request):
    """
    AJAX endpoint for real-time dashboard statistics.
    """
    try:
        stats = _get_dashboard_stats()
        return JsonResponse(stats)
    except Exception as e:
        logger.error(f"Dashboard stats AJAX failed: {e}")
        return JsonResponse({'error': 'Failed to load stats'}, status=500)


# Helper functions
def _get_dashboard_stats():
    """Get dashboard statistics."""
    total_credentials = Credential.objects.count()
    pending_count = Credential.objects.filter(status=Credential.Status.PENDING).count()
    verified_count = Credential.objects.filter(status=Credential.Status.VERIFIED).count()
    invalid_count = Credential.objects.filter(status=Credential.Status.INVALID).count()
    
    # Calculate verification rate
    verification_rate = (verified_count / total_credentials * 100) if total_credentials > 0 else 0
    
    # Get today's activity
    today = timezone.now().date()
    today_verified = Credential.objects.filter(
        verification_date__date=today,
        status=Credential.Status.VERIFIED
    ).count()
    
    # Get average processing time
    recent_verified = Credential.objects.filter(
        status=Credential.Status.VERIFIED,
        verification_date__isnull=False
    ).order_by('-verification_date')[:100]
    
    processing_times = []
    for cred in recent_verified:
        if cred.verification_date and cred.created_at:
            delta = cred.verification_date - cred.created_at
            processing_times.append(delta.total_seconds() / 3600)  # Convert to hours
    
    avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
    
    return {
        'total_credentials': total_credentials,
        'pending_count': pending_count,
        'verified_count': verified_count,
        'invalid_count': invalid_count,
        'verification_rate': round(verification_rate, 1),
        'today_verified': today_verified,
        'avg_processing_time': round(avg_processing_time, 1)
    }


def _get_recent_activity():
    """Get recent verification activity."""
    recent_logs = ComplianceAuditLog.objects.filter(
        action__in=['QUICK_VERIFY_CREDENTIAL', 'QUICK_REJECT_CREDENTIAL', 'CREDENTIAL_VERIFICATION']
    ).order_by('-timestamp')[:10]
    
    activity = []
    for log in recent_logs:
        activity.append({
            'timestamp': log.timestamp,
            'user': log.user_email,
            'action': log.action.replace('_', ' ').title(),
            'resource_id': log.resource_id,
            'details': log.details
        })
    
    return activity


def _get_queue_metrics():
    """Get verification queue metrics."""
    pending_credentials = Credential.objects.filter(status=Credential.Status.PENDING)
    
    # Age distribution
    now = timezone.now()
    age_distribution = {
        'under_1_hour': pending_credentials.filter(created_at__gte=now - timedelta(hours=1)).count(),
        'under_24_hours': pending_credentials.filter(created_at__gte=now - timedelta(days=1)).count(),
        'under_week': pending_credentials.filter(created_at__gte=now - timedelta(days=7)).count(),
        'over_week': pending_credentials.filter(created_at__lt=now - timedelta(days=7)).count()
    }
    
    return {
        'total_pending': pending_credentials.count(),
        'age_distribution': age_distribution
    }


def _get_ai_analysis_alerts():
    """Get AI analysis alerts requiring attention."""
    alerts = []
    
    # Find credentials with low AI confidence
    low_confidence = Credential.objects.filter(
        status=Credential.Status.PENDING,
        ai_extracted_data__ai_verification__overall_confidence__lt=0.6
    )[:5]
    
    for cred in low_confidence:
        alerts.append({
            'type': 'low_confidence',
            'credential': cred,
            'message': f'Low AI confidence ({cred.ai_extracted_data.get("ai_verification", {}).get("overall_confidence", 0):.1%})',
            'severity': 'warning'
        })
    
    # Find credentials with anomalies
    with_anomalies = Credential.objects.filter(
        status=Credential.Status.PENDING
    )[:5]  # Simplified for demo
    
    for cred in with_anomalies:
        ai_data = cred.ai_extracted_data or {}
        anomalies = ai_data.get('ai_verification', {}).get('anomalies_detected', [])
        if anomalies:
            alerts.append({
                'type': 'anomalies',
                'credential': cred,
                'message': f'{len(anomalies)} anomalies detected',
                'severity': 'error'
            })
    
    return alerts


def _calculate_priority_score(credential):
    """Calculate priority score for credential verification."""
    score = 50  # Base score
    
    # Age factor (older = higher priority)
    age_hours = (timezone.now() - credential.created_at).total_seconds() / 3600
    if age_hours > 168:  # Over a week
        score += 30
    elif age_hours > 72:  # Over 3 days
        score += 20
    elif age_hours > 24:  # Over a day
        score += 10
    
    # AI confidence factor (lower confidence = higher priority)
    if credential.ai_extracted_data and 'ai_verification' in credential.ai_extracted_data:
        confidence = credential.ai_extracted_data['ai_verification'].get('overall_confidence', 0.5)
        if confidence < 0.5:
            score += 25
        elif confidence < 0.7:
            score += 15
    
    # Credential type factor (licenses = higher priority)
    if credential.credential_type.category == CredentialType.Category.LICENSE:
        score += 15
    
    # Anomalies factor
    if credential.ai_extracted_data and 'ai_verification' in credential.ai_extracted_data:
        anomalies = credential.ai_extracted_data['ai_verification'].get('anomalies_detected', [])
        if len(anomalies) > 2:
            score += 20
        elif len(anomalies) > 0:
            score += 10
    
    return min(score, 100)  # Cap at 100


def _get_priority_label(score):
    """Get priority label from score."""
    if score >= 80:
        return 'High'
    elif score >= 50:
        return 'Medium'
    else:
        return 'Low'


def _get_verification_analytics(start_date, end_date):
    """Get verification analytics for date range."""
    credentials = Credential.objects.filter(
        verification_date__range=[start_date, end_date]
    )
    
    # Verification by status
    by_status = {
        'verified': credentials.filter(status=Credential.Status.VERIFIED).count(),
        'invalid': credentials.filter(status=Credential.Status.INVALID).count(),
        'expired': credentials.filter(status=Credential.Status.EXPIRED).count()
    }
    
    # Verification by type
    by_type = {}
    for cred_type in CredentialType.objects.all():
        count = credentials.filter(credential_type=cred_type).count()
        if count > 0:
            by_type[cred_type.name] = count
    
    # Daily verification trend
    daily_trend = []
    current_date = start_date.date()
    while current_date <= end_date.date():
        daily_count = credentials.filter(verification_date__date=current_date).count()
        daily_trend.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'count': daily_count
        })
        current_date += timedelta(days=1)
    
    return {
        'by_status': by_status,
        'by_type': by_type,
        'daily_trend': daily_trend,
        'total_verified': sum(by_status.values())
    }


def _get_team_performance(start_date, end_date):
    """Get team performance metrics."""
    # Get verifications by user
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    performance = []
    staff_users = User.objects.filter(is_staff=True)
    
    for user in staff_users:
        verified_count = Credential.objects.filter(
            verified_by=user,
            verification_date__range=[start_date, end_date]
        ).count()
        
        if verified_count > 0:
            performance.append({
                'user': user.get_full_name() or user.email,
                'verified_count': verified_count
            })
    
    # Sort by count
    performance.sort(key=lambda x: x['verified_count'], reverse=True)
    
    return performance


def _get_compliance_metrics(start_date, end_date):
    """Get compliance metrics."""
    audit_logs = ComplianceAuditLog.objects.filter(
        timestamp__range=[start_date, end_date]
    )
    
    return {
        'total_audit_entries': audit_logs.count(),
        'high_risk_activities': audit_logs.filter(risk_level='high').count(),
        'unique_users': audit_logs.values('user_email').distinct().count()
    }
