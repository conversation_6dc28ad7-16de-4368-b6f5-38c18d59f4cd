"""
Code Med Talent - Compliance Middleware
Automatic audit logging for HIPAA compliance.
"""

import logging
import json
from django.utils.deprecation import MiddlewareMixin
from django.urls import resolve
from django.http import JsonResponse
from .compliance import compliance_service

logger = logging.getLogger(__name__)


class HIPAAComplianceMiddleware(MiddlewareMixin):
    """
    Middleware to automatically log API access for HIPAA compliance.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Define which endpoints require audit logging
        self.monitored_endpoints = [
            'credential-upload',
            'credential-verify',
            'credential-verify-primary',
            'credential-verify-ai',
            'credential-verify-blockchain',
            'credential-status',
            'agency-candidate-search',
            'agency-candidate-profile',
            'agency-verification-report',
            'career-passport-create',
            'career-passport-report'
        ]
        
        # Define risk levels for different actions
        self.risk_levels = {
            'GET': 'low',
            'POST': 'medium',
            'PUT': 'medium',
            'PATCH': 'medium',
            'DELETE': 'high'
        }
        
        # High-risk endpoints
        self.high_risk_endpoints = [
            'credential-verify',
            'agency-candidate-search',
            'agency-candidate-profile',
            'career-passport-create'
        ]
    
    def process_request(self, request):
        """Process incoming request for compliance logging."""
        try:
            # Skip non-API requests
            if not request.path.startswith('/api/'):
                return None
            
            # Resolve URL to get endpoint name
            try:
                resolved = resolve(request.path)
                endpoint_name = resolved.url_name
            except:
                endpoint_name = 'unknown'
            
            # Check if this endpoint should be monitored
            if endpoint_name in self.monitored_endpoints:
                # Store request info for later logging
                request._compliance_log_data = {
                    'endpoint_name': endpoint_name,
                    'method': request.method,
                    'path': request.path,
                    'query_params': dict(request.GET),
                    'timestamp': None  # Will be set in process_response
                }
                
                # Determine risk level
                base_risk = self.risk_levels.get(request.method, 'medium')
                if endpoint_name in self.high_risk_endpoints:
                    request._compliance_log_data['risk_level'] = 'high'
                else:
                    request._compliance_log_data['risk_level'] = base_risk
        
        except Exception as e:
            logger.error(f"Compliance middleware request processing failed: {e}")
        
        return None
    
    def process_response(self, request, response):
        """Process response and log access if needed."""
        try:
            # Check if we should log this request
            if hasattr(request, '_compliance_log_data'):
                log_data = request._compliance_log_data
                
                # Extract resource information from response
                resource_type = 'credential'
                resource_id = 'unknown'
                
                # Try to extract resource ID from URL path
                path_parts = request.path.strip('/').split('/')
                for i, part in enumerate(path_parts):
                    if part in ['credentials', 'verify', 'status']:
                        if i + 1 < len(path_parts):
                            resource_id = path_parts[i + 1]
                            break
                
                # Prepare audit details
                audit_details = {
                    'endpoint': log_data['endpoint_name'],
                    'method': log_data['method'],
                    'path': log_data['path'],
                    'query_params': log_data['query_params'],
                    'response_status': response.status_code,
                    'user_authenticated': request.user.is_authenticated if hasattr(request, 'user') else False
                }
                
                # Add request body for POST/PUT requests (but sanitize sensitive data)
                if request.method in ['POST', 'PUT', 'PATCH']:
                    try:
                        if hasattr(request, 'body') and request.body:
                            # Parse JSON body if possible
                            if request.content_type == 'application/json':
                                body_data = json.loads(request.body.decode('utf-8'))
                                # Remove sensitive fields
                                sanitized_body = self._sanitize_request_body(body_data)
                                audit_details['request_body'] = sanitized_body
                    except:
                        audit_details['request_body'] = 'Could not parse request body'
                
                # Log the access
                user = request.user if hasattr(request, 'user') and request.user.is_authenticated else None
                
                compliance_service.log_access(
                    user=user,
                    action=f"{log_data['method']}_{log_data['endpoint_name']}",
                    resource_type=resource_type,
                    resource_id=resource_id,
                    request=request,
                    details=audit_details,
                    risk_level=log_data['risk_level']
                )
        
        except Exception as e:
            logger.error(f"Compliance middleware response processing failed: {e}")
        
        return response
    
    def _sanitize_request_body(self, body_data):
        """Remove sensitive information from request body for logging."""
        if not isinstance(body_data, dict):
            return body_data
        
        sanitized = body_data.copy()
        
        # Remove sensitive fields
        sensitive_fields = [
            'password',
            'credential_number',
            'ssn',
            'date_of_birth',
            'document_file',
            'personal_identifier'
        ]
        
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = '[REDACTED]'
        
        # Recursively sanitize nested objects
        for key, value in sanitized.items():
            if isinstance(value, dict):
                sanitized[key] = self._sanitize_request_body(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_request_body(item) if isinstance(item, dict) else item
                    for item in value
                ]
        
        return sanitized


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers for HIPAA compliance.
    """
    
    def process_response(self, request, response):
        """Add security headers to response."""
        
        # Prevent clickjacking
        response['X-Frame-Options'] = 'DENY'
        
        # Prevent MIME type sniffing
        response['X-Content-Type-Options'] = 'nosniff'
        
        # Enable XSS protection
        response['X-XSS-Protection'] = '1; mode=block'
        
        # Enforce HTTPS
        if not settings.DEBUG:
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # Content Security Policy
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "connect-src 'self'; "
            "font-src 'self'; "
            "object-src 'none'; "
            "media-src 'self'; "
            "frame-src 'none';"
        )
        
        # Referrer Policy
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Permissions Policy (formerly Feature Policy)
        response['Permissions-Policy'] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )
        
        return response


class RateLimitingMiddleware(MiddlewareMixin):
    """
    Simple rate limiting middleware for API protection.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.request_counts = {}  # In production, use Redis or similar
        self.rate_limits = {
            'default': {'requests': 100, 'window': 3600},  # 100 requests per hour
            'upload': {'requests': 10, 'window': 3600},    # 10 uploads per hour
            'verification': {'requests': 50, 'window': 3600}  # 50 verifications per hour
        }
    
    def process_request(self, request):
        """Check rate limits for incoming requests."""
        try:
            # Skip non-API requests
            if not request.path.startswith('/api/'):
                return None
            
            # Get client identifier
            client_id = self._get_client_identifier(request)
            
            # Determine rate limit category
            limit_category = self._get_limit_category(request)
            rate_limit = self.rate_limits.get(limit_category, self.rate_limits['default'])
            
            # Check rate limit
            if self._is_rate_limited(client_id, limit_category, rate_limit):
                return JsonResponse(
                    {
                        'error': 'Rate limit exceeded',
                        'message': f'Too many requests. Limit: {rate_limit["requests"]} per {rate_limit["window"]} seconds'
                    },
                    status=429
                )
        
        except Exception as e:
            logger.error(f"Rate limiting middleware failed: {e}")
        
        return None
    
    def _get_client_identifier(self, request):
        """Get unique identifier for client."""
        # Use user ID if authenticated, otherwise IP address
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user_{request.user.id}"
        else:
            # Get IP address
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR', '0.0.0.0')
            return f"ip_{ip}"
    
    def _get_limit_category(self, request):
        """Determine rate limit category based on request."""
        if 'upload' in request.path:
            return 'upload'
        elif 'verify' in request.path:
            return 'verification'
        else:
            return 'default'
    
    def _is_rate_limited(self, client_id, category, rate_limit):
        """Check if client has exceeded rate limit."""
        import time
        
        current_time = int(time.time())
        window_start = current_time - rate_limit['window']
        
        # Clean old entries
        key = f"{client_id}_{category}"
        if key in self.request_counts:
            self.request_counts[key] = [
                timestamp for timestamp in self.request_counts[key]
                if timestamp > window_start
            ]
        else:
            self.request_counts[key] = []
        
        # Check if limit exceeded
        if len(self.request_counts[key]) >= rate_limit['requests']:
            return True
        
        # Add current request
        self.request_counts[key].append(current_time)
        return False
