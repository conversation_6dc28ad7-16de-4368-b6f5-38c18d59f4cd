"""
Code Med Talent - Primary Source Verification Services
Real-time verification against healthcare credential databases.
"""

import logging
import requests
import time
from typing import Dict, Optional, List, Any
from dataclasses import dataclass
from datetime import datetime, date
import re

from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


@dataclass
class VerificationResult:
    """Result of primary source verification."""
    is_verified: bool
    status: str  # 'verified', 'invalid', 'expired', 'not_found', 'error'
    source: str
    verification_date: datetime
    details: Dict[str, Any]
    confidence_score: float
    error_message: Optional[str] = None


class NursingLicenseVerifier:
    """
    Verifies nursing licenses through state nursing boards.
    Uses NURSYS and state-specific databases.
    """
    
    def __init__(self):
        self.nursys_base_url = "https://www.nursys.com/LQC/LQCSearch.aspx"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Code-Med-Talent-Verification/1.0'
        })

    def verify_rn_license(self, license_number: str, state: str, holder_name: str) -> VerificationResult:
        """Verify RN license through NURSYS or state board."""
        try:
            # First try NURSYS (multi-state database)
            nursys_result = self._verify_through_nursys(license_number, state, holder_name, 'RN')
            if nursys_result.is_verified:
                return nursys_result
            
            # Fallback to state-specific verification
            state_result = self._verify_through_state_board(license_number, state, holder_name, 'RN')
            return state_result
            
        except Exception as e:
            logger.error(f"RN license verification failed: {e}")
            return VerificationResult(
                is_verified=False,
                status='error',
                source='verification_error',
                verification_date=timezone.now(),
                details={'error': str(e)},
                confidence_score=0.0,
                error_message=str(e)
            )

    def verify_lpn_license(self, license_number: str, state: str, holder_name: str) -> VerificationResult:
        """Verify LPN license through state boards."""
        try:
            return self._verify_through_state_board(license_number, state, holder_name, 'LPN')
        except Exception as e:
            logger.error(f"LPN license verification failed: {e}")
            return VerificationResult(
                is_verified=False,
                status='error',
                source='verification_error',
                verification_date=timezone.now(),
                details={'error': str(e)},
                confidence_score=0.0,
                error_message=str(e)
            )

    def _verify_through_nursys(self, license_number: str, state: str, holder_name: str, license_type: str) -> VerificationResult:
        """Verify through NURSYS multi-state database."""
        # Note: This is a simplified implementation
        # In production, you would need proper API access to NURSYS
        
        # Mock verification for demonstration
        # In reality, this would make actual API calls to NURSYS
        mock_verified_licenses = {
            'RN123456': {'name': 'John Doe', 'state': 'CA', 'status': 'active', 'expires': '2024-12-31'},
            'RN789012': {'name': 'Jane Smith', 'state': 'TX', 'status': 'active', 'expires': '2025-06-30'},
        }
        
        if license_number in mock_verified_licenses:
            license_data = mock_verified_licenses[license_number]
            name_match = self._fuzzy_name_match(holder_name, license_data['name'])
            
            return VerificationResult(
                is_verified=name_match and license_data['status'] == 'active',
                status='verified' if name_match and license_data['status'] == 'active' else 'invalid',
                source='NURSYS',
                verification_date=timezone.now(),
                details={
                    'license_number': license_number,
                    'holder_name': license_data['name'],
                    'state': license_data['state'],
                    'status': license_data['status'],
                    'expiration_date': license_data['expires'],
                    'name_match_score': name_match
                },
                confidence_score=0.95 if name_match else 0.3
            )
        
        return VerificationResult(
            is_verified=False,
            status='not_found',
            source='NURSYS',
            verification_date=timezone.now(),
            details={'license_number': license_number, 'searched_state': state},
            confidence_score=0.0
        )

    def _verify_through_state_board(self, license_number: str, state: str, holder_name: str, license_type: str) -> VerificationResult:
        """Verify through state-specific nursing board databases."""
        # State-specific verification URLs and methods
        state_verification_urls = {
            'CA': 'https://www.rn.ca.gov/verification/',
            'TX': 'https://www.bon.texas.gov/verification/',
            'NY': 'https://www.op.nysed.gov/verification/',
            'FL': 'https://floridasnursing.gov/verification/',
        }
        
        if state not in state_verification_urls:
            return VerificationResult(
                is_verified=False,
                status='not_supported',
                source=f'{state}_board',
                verification_date=timezone.now(),
                details={'message': f'State {state} verification not yet supported'},
                confidence_score=0.0
            )
        
        # Mock state verification
        # In production, this would scrape or use APIs for each state
        mock_state_data = {
            'CA': {'RN123456': {'name': 'John Doe', 'status': 'active'}},
            'TX': {'RN789012': {'name': 'Jane Smith', 'status': 'active'}},
        }
        
        state_data = mock_state_data.get(state, {})
        if license_number in state_data:
            license_info = state_data[license_number]
            name_match = self._fuzzy_name_match(holder_name, license_info['name'])
            
            return VerificationResult(
                is_verified=name_match and license_info['status'] == 'active',
                status='verified' if name_match and license_info['status'] == 'active' else 'invalid',
                source=f'{state}_nursing_board',
                verification_date=timezone.now(),
                details=license_info,
                confidence_score=0.90 if name_match else 0.2
            )
        
        return VerificationResult(
            is_verified=False,
            status='not_found',
            source=f'{state}_nursing_board',
            verification_date=timezone.now(),
            details={'license_number': license_number},
            confidence_score=0.0
        )

    def _fuzzy_name_match(self, name1: str, name2: str) -> bool:
        """Fuzzy matching for names to account for variations."""
        if not name1 or not name2:
            return False
        
        # Normalize names
        name1_clean = re.sub(r'[^a-zA-Z\s]', '', name1.lower().strip())
        name2_clean = re.sub(r'[^a-zA-Z\s]', '', name2.lower().strip())
        
        # Exact match
        if name1_clean == name2_clean:
            return True
        
        # Split into parts and check for substantial overlap
        parts1 = set(name1_clean.split())
        parts2 = set(name2_clean.split())
        
        # At least 2 matching parts or 80% overlap
        overlap = len(parts1.intersection(parts2))
        return overlap >= 2 or overlap / max(len(parts1), len(parts2)) >= 0.8


class CertificationVerifier:
    """
    Verifies healthcare certifications (BLS, ACLS, PALS, etc.).
    """
    
    def __init__(self):
        self.aha_base_url = "https://cpr.heart.org/en/course-catalog-search"
        self.session = requests.Session()

    def verify_aha_certification(self, cert_number: str, cert_type: str, holder_name: str) -> VerificationResult:
        """Verify AHA certifications (BLS, ACLS, PALS)."""
        try:
            # Mock AHA verification
            # In production, this would integrate with AHA's verification system
            mock_aha_certs = {
                'BLS123456': {'name': 'John Doe', 'type': 'BLS', 'status': 'valid', 'expires': '2024-12-31'},
                'ACLS789012': {'name': 'Jane Smith', 'type': 'ACLS', 'status': 'valid', 'expires': '2025-03-15'},
                'PALS345678': {'name': 'Bob Johnson', 'type': 'PALS', 'status': 'valid', 'expires': '2024-09-30'},
            }
            
            if cert_number in mock_aha_certs:
                cert_data = mock_aha_certs[cert_number]
                name_match = self._fuzzy_name_match(holder_name, cert_data['name'])
                type_match = cert_type.upper() in cert_data['type'].upper()
                
                # Check if expired
                exp_date = datetime.strptime(cert_data['expires'], '%Y-%m-%d').date()
                is_expired = exp_date < date.today()
                
                status = 'verified'
                if is_expired:
                    status = 'expired'
                elif not (name_match and type_match):
                    status = 'invalid'
                
                return VerificationResult(
                    is_verified=name_match and type_match and not is_expired,
                    status=status,
                    source='American_Heart_Association',
                    verification_date=timezone.now(),
                    details={
                        'certification_number': cert_number,
                        'holder_name': cert_data['name'],
                        'certification_type': cert_data['type'],
                        'expiration_date': cert_data['expires'],
                        'name_match': name_match,
                        'type_match': type_match,
                        'is_expired': is_expired
                    },
                    confidence_score=0.95 if name_match and type_match else 0.3
                )
            
            return VerificationResult(
                is_verified=False,
                status='not_found',
                source='American_Heart_Association',
                verification_date=timezone.now(),
                details={'certification_number': cert_number, 'certification_type': cert_type},
                confidence_score=0.0
            )
            
        except Exception as e:
            logger.error(f"AHA certification verification failed: {e}")
            return VerificationResult(
                is_verified=False,
                status='error',
                source='verification_error',
                verification_date=timezone.now(),
                details={'error': str(e)},
                confidence_score=0.0,
                error_message=str(e)
            )

    def _fuzzy_name_match(self, name1: str, name2: str) -> bool:
        """Fuzzy matching for names."""
        if not name1 or not name2:
            return False
        
        name1_clean = re.sub(r'[^a-zA-Z\s]', '', name1.lower().strip())
        name2_clean = re.sub(r'[^a-zA-Z\s]', '', name2.lower().strip())
        
        if name1_clean == name2_clean:
            return True
        
        parts1 = set(name1_clean.split())
        parts2 = set(name2_clean.split())
        overlap = len(parts1.intersection(parts2))
        return overlap >= 2 or overlap / max(len(parts1), len(parts2)) >= 0.8


class EducationVerifier:
    """
    Verifies educational credentials through National Student Clearinghouse.
    """
    
    def __init__(self):
        self.nsc_base_url = "https://www.studentclearinghouse.org/"

    def verify_degree(self, degree_type: str, institution: str, holder_name: str, graduation_date: str) -> VerificationResult:
        """Verify degree through National Student Clearinghouse."""
        try:
            # Mock NSC verification
            # In production, this would integrate with NSC's verification service
            mock_degrees = {
                ('John Doe', 'BSN', 'University of California'): {'verified': True, 'graduation': '2020-05-15'},
                ('Jane Smith', 'ADN', 'Community College of Denver'): {'verified': True, 'graduation': '2019-12-20'},
            }
            
            key = (holder_name, degree_type, institution)
            if key in mock_degrees:
                degree_data = mock_degrees[key]
                
                return VerificationResult(
                    is_verified=degree_data['verified'],
                    status='verified' if degree_data['verified'] else 'invalid',
                    source='National_Student_Clearinghouse',
                    verification_date=timezone.now(),
                    details={
                        'degree_type': degree_type,
                        'institution': institution,
                        'holder_name': holder_name,
                        'graduation_date': degree_data['graduation']
                    },
                    confidence_score=0.98 if degree_data['verified'] else 0.0
                )
            
            return VerificationResult(
                is_verified=False,
                status='not_found',
                source='National_Student_Clearinghouse',
                verification_date=timezone.now(),
                details={'degree_type': degree_type, 'institution': institution},
                confidence_score=0.0
            )
            
        except Exception as e:
            logger.error(f"Education verification failed: {e}")
            return VerificationResult(
                is_verified=False,
                status='error',
                source='verification_error',
                verification_date=timezone.now(),
                details={'error': str(e)},
                confidence_score=0.0,
                error_message=str(e)
            )


class PrimarySourceVerificationService:
    """
    Main service that coordinates verification across different sources.
    """
    
    def __init__(self):
        self.nursing_verifier = NursingLicenseVerifier()
        self.certification_verifier = CertificationVerifier()
        self.education_verifier = EducationVerifier()

    def verify_credential(self, credential_type: str, credential_data: Dict[str, Any]) -> VerificationResult:
        """
        Route verification to appropriate service based on credential type.
        """
        credential_type_lower = credential_type.lower()
        
        try:
            if 'rn license' in credential_type_lower:
                return self.nursing_verifier.verify_rn_license(
                    credential_data.get('license_number', ''),
                    credential_data.get('state', ''),
                    credential_data.get('holder_name', '')
                )
            
            elif 'lpn license' in credential_type_lower:
                return self.nursing_verifier.verify_lpn_license(
                    credential_data.get('license_number', ''),
                    credential_data.get('state', ''),
                    credential_data.get('holder_name', '')
                )
            
            elif any(cert in credential_type_lower for cert in ['bls', 'acls', 'pals']):
                cert_type = 'BLS' if 'bls' in credential_type_lower else \
                           'ACLS' if 'acls' in credential_type_lower else 'PALS'
                return self.certification_verifier.verify_aha_certification(
                    credential_data.get('certification_number', ''),
                    cert_type,
                    credential_data.get('holder_name', '')
                )
            
            elif any(degree in credential_type_lower for degree in ['bsn', 'adn', 'bachelor', 'associate']):
                degree_type = 'BSN' if 'bsn' in credential_type_lower or 'bachelor' in credential_type_lower else 'ADN'
                return self.education_verifier.verify_degree(
                    degree_type,
                    credential_data.get('institution', ''),
                    credential_data.get('holder_name', ''),
                    credential_data.get('graduation_date', '')
                )
            
            else:
                return VerificationResult(
                    is_verified=False,
                    status='not_supported',
                    source='primary_verification',
                    verification_date=timezone.now(),
                    details={'message': f'Verification not yet supported for {credential_type}'},
                    confidence_score=0.0
                )
                
        except Exception as e:
            logger.error(f"Primary source verification failed: {e}")
            return VerificationResult(
                is_verified=False,
                status='error',
                source='verification_error',
                verification_date=timezone.now(),
                details={'error': str(e)},
                confidence_score=0.0,
                error_message=str(e)
            )
