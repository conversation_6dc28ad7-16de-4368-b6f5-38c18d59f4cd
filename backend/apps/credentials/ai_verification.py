"""
Code Med Talent - AI Verification Engine
Advanced AI-powered verification logic with confidence scoring.
"""

import logging
import re
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, date
import difflib

from django.conf import settings
from django.utils import timezone

# AI/ML imports
try:
    import openai
    from textdistance import le<PERSON><PERSON>tein, jaro_winkler
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class VerificationScore:
    """Comprehensive verification scoring."""
    overall_confidence: float
    ocr_quality_score: float
    data_consistency_score: float
    format_compliance_score: float
    cross_reference_score: float
    ai_analysis_score: float
    risk_factors: List[str]
    recommendations: List[str]


@dataclass
class AIVerificationResult:
    """Result of AI verification analysis."""
    is_likely_valid: bool
    confidence_score: float
    verification_scores: VerificationScore
    extracted_insights: Dict[str, Any]
    anomalies_detected: List[str]
    verification_recommendation: str
    processing_notes: str


class AIVerificationEngine:
    """
    Advanced AI engine for credential verification analysis.
    """
    
    def __init__(self):
        self.openai_client = None
        if OPENAI_AVAILABLE and hasattr(settings, 'OPENAI_API_KEY'):
            openai.api_key = settings.OPENAI_API_KEY
            self.openai_client = openai
        
        # Healthcare credential patterns and validation rules
        self.credential_patterns = {
            'rn_license': {
                'number_patterns': [r'RN\d{6,8}', r'\d{6,8}', r'[A-Z]{2}\d{6}'],
                'required_fields': ['license_number', 'holder_name', 'state', 'expiration_date'],
                'typical_validity_months': 24,
                'issuing_authorities': ['State Board of Nursing', 'Department of Health']
            },
            'bls_certification': {
                'number_patterns': [r'BLS\d{6,8}', r'\d{8,10}', r'[A-Z]{3}\d{6}'],
                'required_fields': ['certification_number', 'holder_name', 'expiration_date'],
                'typical_validity_months': 24,
                'issuing_authorities': ['American Heart Association', 'American Red Cross']
            },
            'degree': {
                'required_fields': ['degree_type', 'institution', 'holder_name', 'graduation_date'],
                'degree_types': ['BSN', 'ADN', 'MSN', 'DNP', 'PhD'],
                'never_expires': True
            }
        }

    def analyze_credential(self, 
                          ocr_result: Dict[str, Any], 
                          extracted_data: Dict[str, Any],
                          primary_verification_result: Optional[Dict[str, Any]] = None,
                          credential_type: str = None) -> AIVerificationResult:
        """
        Comprehensive AI analysis of credential data.
        """
        try:
            # Step 1: Analyze OCR quality
            ocr_quality_score = self._analyze_ocr_quality(ocr_result)
            
            # Step 2: Check data consistency
            consistency_score = self._analyze_data_consistency(extracted_data)
            
            # Step 3: Validate format compliance
            format_score = self._analyze_format_compliance(extracted_data, credential_type)
            
            # Step 4: Cross-reference with primary verification
            cross_ref_score = self._analyze_cross_reference(extracted_data, primary_verification_result)
            
            # Step 5: AI-powered deep analysis
            ai_analysis_score, ai_insights = self._perform_ai_analysis(ocr_result, extracted_data, credential_type)
            
            # Step 6: Detect anomalies
            anomalies = self._detect_anomalies(extracted_data, credential_type)
            
            # Step 7: Calculate overall confidence
            verification_scores = VerificationScore(
                overall_confidence=self._calculate_overall_confidence(
                    ocr_quality_score, consistency_score, format_score, 
                    cross_ref_score, ai_analysis_score
                ),
                ocr_quality_score=ocr_quality_score,
                data_consistency_score=consistency_score,
                format_compliance_score=format_score,
                cross_reference_score=cross_ref_score,
                ai_analysis_score=ai_analysis_score,
                risk_factors=self._identify_risk_factors(extracted_data, anomalies),
                recommendations=self._generate_recommendations(
                    ocr_quality_score, consistency_score, format_score, anomalies
                )
            )
            
            # Step 8: Generate final recommendation
            recommendation = self._generate_verification_recommendation(verification_scores)
            
            return AIVerificationResult(
                is_likely_valid=verification_scores.overall_confidence >= 0.7,
                confidence_score=verification_scores.overall_confidence,
                verification_scores=verification_scores,
                extracted_insights=ai_insights,
                anomalies_detected=anomalies,
                verification_recommendation=recommendation,
                processing_notes=f"AI analysis completed with {len(anomalies)} anomalies detected"
            )
            
        except Exception as e:
            logger.error(f"AI verification analysis failed: {e}")
            return AIVerificationResult(
                is_likely_valid=False,
                confidence_score=0.0,
                verification_scores=VerificationScore(
                    overall_confidence=0.0,
                    ocr_quality_score=0.0,
                    data_consistency_score=0.0,
                    format_compliance_score=0.0,
                    cross_reference_score=0.0,
                    ai_analysis_score=0.0,
                    risk_factors=['AI analysis error'],
                    recommendations=['Manual review required']
                ),
                extracted_insights={'error': str(e)},
                anomalies_detected=['AI processing error'],
                verification_recommendation='MANUAL_REVIEW_REQUIRED',
                processing_notes=f"AI analysis failed: {str(e)}"
            )

    def _analyze_ocr_quality(self, ocr_result: Dict[str, Any]) -> float:
        """Analyze the quality of OCR extraction."""
        if not ocr_result:
            return 0.0
        
        score = 0.0
        factors = 0
        
        # OCR confidence from the engine
        if 'ocr_confidence' in ocr_result:
            score += ocr_result['ocr_confidence']
            factors += 1
        
        # Text length and completeness
        text = ocr_result.get('ocr_text', '')
        if len(text) > 50:  # Reasonable amount of text extracted
            score += 0.8
        elif len(text) > 20:
            score += 0.5
        else:
            score += 0.2
        factors += 1
        
        # Check for common OCR artifacts
        artifacts = ['|||', '###', '???', '***', '...']
        artifact_count = sum(1 for artifact in artifacts if artifact in text)
        artifact_penalty = min(artifact_count * 0.1, 0.3)
        score += max(0.7 - artifact_penalty, 0.0)
        factors += 1
        
        # Character recognition quality
        alpha_ratio = sum(c.isalpha() for c in text) / max(len(text), 1)
        if alpha_ratio > 0.6:
            score += 0.8
        elif alpha_ratio > 0.4:
            score += 0.5
        else:
            score += 0.2
        factors += 1
        
        return score / factors if factors > 0 else 0.0

    def _analyze_data_consistency(self, extracted_data: Dict[str, Any]) -> float:
        """Analyze consistency of extracted data."""
        if not extracted_data:
            return 0.0
        
        score = 0.0
        checks = 0
        
        # Name consistency
        holder_name = extracted_data.get('holder_name', '')
        if holder_name and len(holder_name.split()) >= 2:
            score += 0.8
        elif holder_name:
            score += 0.4
        checks += 1
        
        # Date format consistency
        dates = ['issue_date', 'expiration_date', 'graduation_date']
        valid_dates = 0
        for date_field in dates:
            if date_field in extracted_data:
                date_value = extracted_data[date_field]
                if self._is_valid_date_format(date_value):
                    valid_dates += 1
        
        if valid_dates > 0:
            score += 0.9
        checks += 1
        
        # License/certification number format
        number_fields = ['license_number', 'certification_number', 'credential_number']
        for field in number_fields:
            if field in extracted_data:
                number = extracted_data[field]
                if number and len(str(number)) >= 4:
                    score += 0.8
                    break
        checks += 1
        
        # State format (if present)
        state = extracted_data.get('state', '')
        if state:
            if len(state) == 2 and state.isupper():
                score += 0.9
            elif len(state) > 2:
                score += 0.6
        checks += 1
        
        return score / checks if checks > 0 else 0.0

    def _analyze_format_compliance(self, extracted_data: Dict[str, Any], credential_type: str) -> float:
        """Analyze compliance with expected credential formats."""
        if not credential_type:
            return 0.5
        
        # Normalize credential type
        cred_type_key = self._normalize_credential_type(credential_type)
        patterns = self.credential_patterns.get(cred_type_key, {})
        
        if not patterns:
            return 0.5  # Unknown type, neutral score
        
        score = 0.0
        checks = 0
        
        # Check required fields
        required_fields = patterns.get('required_fields', [])
        present_fields = sum(1 for field in required_fields if extracted_data.get(field))
        if required_fields:
            score += (present_fields / len(required_fields)) * 0.8
            checks += 1
        
        # Check number patterns
        number_patterns = patterns.get('number_patterns', [])
        if number_patterns:
            number_value = (extracted_data.get('license_number') or 
                          extracted_data.get('certification_number') or 
                          extracted_data.get('credential_number', ''))
            
            pattern_match = any(re.match(pattern, str(number_value)) for pattern in number_patterns)
            score += 0.9 if pattern_match else 0.3
            checks += 1
        
        # Check issuing authority
        expected_authorities = patterns.get('issuing_authorities', [])
        if expected_authorities:
            issuing_auth = extracted_data.get('issuing_authority', '')
            authority_match = any(auth.lower() in issuing_auth.lower() 
                                for auth in expected_authorities)
            score += 0.8 if authority_match else 0.4
            checks += 1
        
        return score / checks if checks > 0 else 0.5

    def _analyze_cross_reference(self, extracted_data: Dict[str, Any], 
                               primary_verification: Optional[Dict[str, Any]]) -> float:
        """Analyze cross-reference with primary source verification."""
        if not primary_verification:
            return 0.5  # No primary verification available
        
        if not primary_verification.get('is_verified'):
            return 0.1  # Primary verification failed
        
        score = 0.0
        checks = 0
        
        # Compare names
        extracted_name = extracted_data.get('holder_name', '')
        verified_name = primary_verification.get('details', {}).get('holder_name', '')
        if extracted_name and verified_name:
            name_similarity = self._calculate_name_similarity(extracted_name, verified_name)
            score += name_similarity
            checks += 1
        
        # Compare license/certification numbers
        extracted_number = (extracted_data.get('license_number') or 
                          extracted_data.get('certification_number') or 
                          extracted_data.get('credential_number', ''))
        verified_number = primary_verification.get('details', {}).get('license_number', '')
        if extracted_number and verified_number:
            number_match = str(extracted_number).strip() == str(verified_number).strip()
            score += 0.95 if number_match else 0.2
            checks += 1
        
        # Compare states
        extracted_state = extracted_data.get('state', '')
        verified_state = primary_verification.get('details', {}).get('state', '')
        if extracted_state and verified_state:
            state_match = extracted_state.upper() == verified_state.upper()
            score += 0.9 if state_match else 0.3
            checks += 1
        
        # Primary verification confidence
        verification_confidence = primary_verification.get('confidence_score', 0.0)
        score += verification_confidence
        checks += 1
        
        return score / checks if checks > 0 else 0.5

    def _perform_ai_analysis(self, ocr_result: Dict[str, Any], 
                           extracted_data: Dict[str, Any], 
                           credential_type: str) -> Tuple[float, Dict[str, Any]]:
        """Perform deep AI analysis using OpenAI."""
        if not self.openai_client:
            return 0.5, {'note': 'OpenAI not available'}
        
        try:
            prompt = f"""
            Analyze this healthcare credential data for authenticity and completeness:
            
            Credential Type: {credential_type}
            OCR Text: {ocr_result.get('ocr_text', '')[:1000]}
            Extracted Data: {json.dumps(extracted_data, indent=2)}
            
            Please analyze:
            1. Does the extracted data match the OCR text?
            2. Are there any inconsistencies or red flags?
            3. Does the format match typical {credential_type} documents?
            4. Rate the overall authenticity (0-1 scale)
            5. Identify any specific concerns
            
            Return a JSON response with:
            - authenticity_score (0-1)
            - consistency_rating (0-1)
            - concerns (list of strings)
            - recommendations (list of strings)
            """
            
            response = self.openai_client.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.1
            )
            
            ai_response = json.loads(response.choices[0].message.content)
            
            # Calculate AI analysis score
            authenticity = ai_response.get('authenticity_score', 0.5)
            consistency = ai_response.get('consistency_rating', 0.5)
            ai_score = (authenticity + consistency) / 2
            
            return ai_score, ai_response
            
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return 0.5, {'error': str(e), 'note': 'AI analysis failed'}

    def _detect_anomalies(self, extracted_data: Dict[str, Any], credential_type: str) -> List[str]:
        """Detect potential anomalies in the credential data."""
        anomalies = []
        
        # Check for future dates
        for date_field in ['issue_date', 'expiration_date']:
            if date_field in extracted_data:
                date_str = extracted_data[date_field]
                if self._is_future_date(date_str):
                    anomalies.append(f"Future {date_field}: {date_str}")
        
        # Check for expired credentials
        exp_date = extracted_data.get('expiration_date')
        if exp_date and self._is_past_date(exp_date):
            anomalies.append(f"Expired credential: {exp_date}")
        
        # Check for unusual name patterns
        name = extracted_data.get('holder_name', '')
        if name:
            if len(name) < 3:
                anomalies.append("Unusually short name")
            elif not re.match(r'^[A-Za-z\s\-\.]+$', name):
                anomalies.append("Name contains unusual characters")
        
        # Check for missing critical fields
        critical_fields = ['holder_name', 'license_number', 'certification_number', 'credential_number']
        if not any(extracted_data.get(field) for field in critical_fields):
            anomalies.append("Missing critical identification fields")
        
        return anomalies

    def _calculate_overall_confidence(self, ocr_quality: float, consistency: float, 
                                    format_compliance: float, cross_ref: float, 
                                    ai_analysis: float) -> float:
        """Calculate weighted overall confidence score."""
        weights = {
            'ocr_quality': 0.15,
            'consistency': 0.20,
            'format_compliance': 0.20,
            'cross_reference': 0.30,
            'ai_analysis': 0.15
        }
        
        weighted_score = (
            ocr_quality * weights['ocr_quality'] +
            consistency * weights['consistency'] +
            format_compliance * weights['format_compliance'] +
            cross_ref * weights['cross_reference'] +
            ai_analysis * weights['ai_analysis']
        )
        
        return min(max(weighted_score, 0.0), 1.0)

    def _identify_risk_factors(self, extracted_data: Dict[str, Any], anomalies: List[str]) -> List[str]:
        """Identify risk factors for manual review."""
        risk_factors = []
        
        if anomalies:
            risk_factors.extend([f"Anomaly: {anomaly}" for anomaly in anomalies])
        
        # Low data completeness
        total_fields = len(extracted_data)
        filled_fields = sum(1 for v in extracted_data.values() if v)
        if filled_fields / max(total_fields, 1) < 0.5:
            risk_factors.append("Low data completeness")
        
        # Missing expiration date for expiring credentials
        if not extracted_data.get('expiration_date'):
            risk_factors.append("Missing expiration date")
        
        return risk_factors

    def _generate_recommendations(self, ocr_quality: float, consistency: float, 
                                format_compliance: float, anomalies: List[str]) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        if ocr_quality < 0.6:
            recommendations.append("Consider re-scanning document with higher quality")
        
        if consistency < 0.6:
            recommendations.append("Verify data consistency manually")
        
        if format_compliance < 0.6:
            recommendations.append("Check document format against credential type standards")
        
        if anomalies:
            recommendations.append("Review detected anomalies before approval")
        
        if not recommendations:
            recommendations.append("Document appears to meet verification standards")
        
        return recommendations

    def _generate_verification_recommendation(self, scores: VerificationScore) -> str:
        """Generate final verification recommendation."""
        if scores.overall_confidence >= 0.9:
            return "AUTO_APPROVE"
        elif scores.overall_confidence >= 0.7:
            return "APPROVE_WITH_REVIEW"
        elif scores.overall_confidence >= 0.4:
            return "MANUAL_REVIEW_REQUIRED"
        else:
            return "REJECT_OR_RESUBMIT"

    # Helper methods
    def _normalize_credential_type(self, credential_type: str) -> str:
        """Normalize credential type for pattern matching."""
        cred_lower = credential_type.lower()
        if 'rn' in cred_lower and 'license' in cred_lower:
            return 'rn_license'
        elif 'bls' in cred_lower:
            return 'bls_certification'
        elif any(degree in cred_lower for degree in ['bsn', 'adn', 'bachelor', 'associate']):
            return 'degree'
        return 'unknown'

    def _is_valid_date_format(self, date_str: str) -> bool:
        """Check if date string is in valid format."""
        if not date_str:
            return False
        
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'\d{1,2}-\d{1,2}-\d{4}'
        ]
        
        return any(re.match(pattern, str(date_str)) for pattern in date_patterns)

    def _is_future_date(self, date_str: str) -> bool:
        """Check if date is in the future."""
        try:
            # Try multiple date formats
            for fmt in ['%m/%d/%Y', '%Y-%m-%d', '%m-%d-%Y']:
                try:
                    parsed_date = datetime.strptime(str(date_str), fmt).date()
                    return parsed_date > date.today()
                except ValueError:
                    continue
            return False
        except:
            return False

    def _is_past_date(self, date_str: str) -> bool:
        """Check if date is in the past."""
        try:
            for fmt in ['%m/%d/%Y', '%Y-%m-%d', '%m-%d-%Y']:
                try:
                    parsed_date = datetime.strptime(str(date_str), fmt).date()
                    return parsed_date < date.today()
                except ValueError:
                    continue
            return False
        except:
            return False

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two names."""
        if not name1 or not name2:
            return 0.0
        
        # Normalize names
        name1_clean = re.sub(r'[^a-zA-Z\s]', '', name1.lower().strip())
        name2_clean = re.sub(r'[^a-zA-Z\s]', '', name2.lower().strip())
        
        # Exact match
        if name1_clean == name2_clean:
            return 1.0
        
        # Use Jaro-Winkler similarity if available
        if OPENAI_AVAILABLE:
            try:
                return jaro_winkler(name1_clean, name2_clean)
            except:
                pass
        
        # Fallback to simple similarity
        return difflib.SequenceMatcher(None, name1_clean, name2_clean).ratio()
