"""
Code Med Talent - Credentials Admin Interface
Enhanced admin interface for credential verification workflow.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse, path
from django.utils.safestring import mark_safe
from django.db.models import Count, Q
from django.contrib.admin import SimpleListFilter
from django.http import HttpResponseRedirect
from django.utils import timezone
import json

from .models import CredentialType, Credential


class CredentialStatusFilter(SimpleListFilter):
    """Filter credentials by verification status."""
    title = 'Verification Status'
    parameter_name = 'verification_status'

    def lookups(self, request, model_admin):
        return (
            ('pending', 'Pending Review'),
            ('verified', 'Verified'),
            ('invalid', 'Invalid/Rejected'),
            ('expired', 'Expired'),
            ('revoked', 'Revoked'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'pending':
            return queryset.filter(status=Credential.Status.PENDING)
        elif self.value() == 'verified':
            return queryset.filter(status=Credential.Status.VERIFIED)
        elif self.value() == 'invalid':
            return queryset.filter(status=Credential.Status.INVALID)
        elif self.value() == 'expired':
            return queryset.filter(status=Credential.Status.EXPIRED)
        elif self.value() == 'revoked':
            return queryset.filter(status=Credential.Status.REVOKED)
        return queryset


class HasBlockchainHashFilter(SimpleListFilter):
    """Filter credentials by blockchain hash presence."""
    title = 'Blockchain Status'
    parameter_name = 'blockchain_status'

    def lookups(self, request, model_admin):
        return (
            ('with_hash', 'Has Blockchain Hash'),
            ('without_hash', 'No Blockchain Hash'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'with_hash':
            return queryset.exclude(blockchain_hash__isnull=True).exclude(blockchain_hash='')
        elif self.value() == 'without_hash':
            return queryset.filter(Q(blockchain_hash__isnull=True) | Q(blockchain_hash=''))
        return queryset


@admin.register(CredentialType)
class CredentialTypeAdmin(admin.ModelAdmin):
    """Admin interface for CredentialType model."""

    list_display = [
        'name',
        'category',
        'verification_source',
        'default_validity_months',
        'has_expiration',
        'is_active',
        'credential_count'
    ]
    list_filter = ['category', 'is_active', 'has_expiration']
    search_fields = ['name', 'description', 'verification_source']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'category', 'description', 'is_active')
        }),
        ('Verification Details', {
            'fields': ('verification_source', 'verification_url', 'default_validity_months', 'has_expiration')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def credential_count(self, obj):
        """Show count of credentials of this type."""
        count = obj.credential_set.count()
        if count > 0:
            url = reverse('admin:credentials_credential_changelist') + f'?credential_type__id__exact={obj.id}'
            return format_html('<a href="{}">{} credentials</a>', url, count)
        return '0 credentials'
    credential_count.short_description = 'Credentials'


@admin.register(Credential)
class CredentialAdmin(admin.ModelAdmin):
    """Enhanced admin interface for Credential model."""

    list_display = [
        'credential_display',
        'user_display',
        'credential_type',
        'status_display',
        'verification_confidence',
        'expiration_status',
        'blockchain_status',
        'verification_actions'
    ]

    list_filter = [
        CredentialStatusFilter,
        HasBlockchainHashFilter,
        'credential_type',
        'verification_source',
        'created_at',
        'verification_date'
    ]

    search_fields = [
        'user__email',
        'user__first_name',
        'user__last_name',
        'credential_number',
        'holder_name',
        'issuing_authority'
    ]

    readonly_fields = [
        'created_at',
        'updated_at',
        'ai_verification_summary',
        'ocr_text_preview',
        'blockchain_hash',
        'verification_timeline'
    ]

    fieldsets = (
        ('Credential Information', {
            'fields': (
                'user',
                'credential_type',
                'credential_number',
                'holder_name',
                'issuing_authority',
                'issuing_state'
            )
        }),
        ('Dates', {
            'fields': ('issue_date', 'expiration_date')
        }),
        ('Verification Status', {
            'fields': (
                'status',
                'verification_date',
                'verification_source',
                'verification_notes',
                'verified_by'
            )
        }),
        ('Document', {
            'fields': ('document_file', 'ocr_text_preview'),
            'classes': ('collapse',)
        }),
        ('AI Analysis', {
            'fields': ('ai_verification_summary',),
            'classes': ('collapse',)
        }),
        ('Blockchain', {
            'fields': ('blockchain_hash',),
            'classes': ('collapse',)
        }),
        ('Timeline', {
            'fields': ('verification_timeline', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    actions = [
        'mark_as_verified',
        'mark_as_invalid',
        'mark_as_pending',
        'trigger_primary_verification',
        'run_ai_analysis'
    ]

    def credential_display(self, obj):
        """Display credential with type and number."""
        return f"{obj.credential_type.name} - {obj.credential_number or 'No Number'}"
    credential_display.short_description = 'Credential'

    def user_display(self, obj):
        """Display user information."""
        user_name = obj.user.get_full_name() or obj.user.email
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:accounts_user_change', args=[obj.user.id]),
            user_name
        )
    user_display.short_description = 'User'

    def status_display(self, obj):
        """Display status with color coding."""
        colors = {
            Credential.Status.PENDING: '#ffc107',  # Yellow
            Credential.Status.VERIFIED: '#28a745',  # Green
            Credential.Status.INVALID: '#dc3545',   # Red
            Credential.Status.EXPIRED: '#6c757d',   # Gray
            Credential.Status.REVOKED: '#fd7e14',   # Orange
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_display.short_description = 'Status'

    def verification_confidence(self, obj):
        """Display AI verification confidence."""
        if obj.ai_extracted_data and 'ai_verification' in obj.ai_extracted_data:
            confidence = obj.ai_extracted_data['ai_verification'].get('overall_confidence', 0)
            percentage = int(confidence * 100)

            if percentage >= 80:
                color = '#28a745'  # Green
            elif percentage >= 60:
                color = '#ffc107'  # Yellow
            else:
                color = '#dc3545'  # Red

            return format_html(
                '<span style="color: {}; font-weight: bold;">{}%</span>',
                color,
                percentage
            )
        return '-'
    verification_confidence.short_description = 'AI Confidence'

    def expiration_status(self, obj):
        """Display expiration status."""
        if not obj.expiration_date:
            return format_html('<span style="color: #6c757d;">No Expiration</span>')

        if obj.is_expired():
            return format_html('<span style="color: #dc3545; font-weight: bold;">Expired</span>')

        days_left = obj.days_until_expiration()
        if days_left is not None:
            if days_left <= 30:
                color = '#ffc107'  # Yellow for expiring soon
            else:
                color = '#28a745'  # Green for valid

            return format_html(
                '<span style="color: {};">{} days</span>',
                color,
                days_left
            )
        return '-'
    expiration_status.short_description = 'Expiration'

    def blockchain_status(self, obj):
        """Display blockchain status."""
        if obj.blockchain_hash:
            return format_html(
                '<span style="color: #28a745;">✓ On Chain</span>'
            )
        return format_html('<span style="color: #6c757d;">Not Stored</span>')
    blockchain_status.short_description = 'Blockchain'

    def verification_actions(self, obj):
        """Display quick verification actions."""
        actions = []

        if obj.status == Credential.Status.PENDING:
            actions.append(
                f'<a href="#" onclick="markAsVerified(\'{obj.id}\')" '
                f'style="color: #28a745; text-decoration: none;">✓ Verify</a>'
            )
            actions.append(
                f'<a href="#" onclick="markAsInvalid(\'{obj.id}\')" '
                f'style="color: #dc3545; text-decoration: none;">✗ Reject</a>'
            )

        return format_html(' | '.join(actions)) if actions else '-'
    verification_actions.short_description = 'Quick Actions'

    def ai_verification_summary(self, obj):
        """Display AI verification summary."""
        if not obj.ai_extracted_data or 'ai_verification' not in obj.ai_extracted_data:
            return 'No AI analysis available'

        ai_data = obj.ai_extracted_data['ai_verification']

        summary_html = f"""
        <div style="font-family: monospace; font-size: 12px;">
            <strong>Overall Confidence:</strong> {ai_data.get('overall_confidence', 0):.2f}<br>
            <strong>Recommendation:</strong> {ai_data.get('verification_recommendation', 'N/A')}<br>
            <strong>Anomalies:</strong> {len(ai_data.get('anomalies_detected', []))}<br>
            <strong>Risk Factors:</strong> {len(ai_data.get('risk_factors', []))}<br>
        </div>
        """

        if ai_data.get('anomalies_detected'):
            summary_html += "<br><strong>Detected Anomalies:</strong><ul>"
            for anomaly in ai_data['anomalies_detected'][:3]:  # Show first 3
                summary_html += f"<li>{anomaly}</li>"
            summary_html += "</ul>"

        return mark_safe(summary_html)
    ai_verification_summary.short_description = 'AI Analysis Summary'

    def ocr_text_preview(self, obj):
        """Display OCR text preview."""
        if obj.ai_extracted_data and 'ocr_text' in obj.ai_extracted_data:
            text = obj.ai_extracted_data['ocr_text']
            preview = text[:200] + '...' if len(text) > 200 else text
            return format_html(
                '<div style="font-family: monospace; font-size: 11px; '
                'background: #f8f9fa; padding: 10px; border-radius: 4px;">{}</div>',
                preview
            )
        return 'No OCR text available'
    ocr_text_preview.short_description = 'OCR Text Preview'

    def verification_timeline(self, obj):
        """Display verification timeline."""
        timeline_html = f"""
        <div style="font-family: monospace; font-size: 12px;">
            <strong>Created:</strong> {obj.created_at.strftime('%Y-%m-%d %H:%M')}<br>
        """

        if obj.verification_date:
            timeline_html += f"<strong>Verified:</strong> {obj.verification_date.strftime('%Y-%m-%d %H:%M')}<br>"

        if obj.verified_by:
            timeline_html += f"<strong>Verified By:</strong> {obj.verified_by.get_full_name()}<br>"

        timeline_html += f"<strong>Last Updated:</strong> {obj.updated_at.strftime('%Y-%m-%d %H:%M')}"
        timeline_html += "</div>"

        return mark_safe(timeline_html)
    verification_timeline.short_description = 'Verification Timeline'

    # Admin Actions
    def mark_as_verified(self, request, queryset):
        """Mark selected credentials as verified."""
        updated = queryset.update(
            status=Credential.Status.VERIFIED,
            verified_by=request.user,
            verification_date=timezone.now()
        )
        self.message_user(request, f'{updated} credentials marked as verified.')
    mark_as_verified.short_description = 'Mark selected credentials as verified'

    def mark_as_invalid(self, request, queryset):
        """Mark selected credentials as invalid."""
        updated = queryset.update(
            status=Credential.Status.INVALID,
            verified_by=request.user,
            verification_date=timezone.now()
        )
        self.message_user(request, f'{updated} credentials marked as invalid.')
    mark_as_invalid.short_description = 'Mark selected credentials as invalid'

    def mark_as_pending(self, request, queryset):
        """Mark selected credentials as pending."""
        updated = queryset.update(status=Credential.Status.PENDING)
        self.message_user(request, f'{updated} credentials marked as pending review.')
    mark_as_pending.short_description = 'Mark selected credentials as pending'

    def trigger_primary_verification(self, request, queryset):
        """Trigger primary source verification for selected credentials."""
        # This would integrate with the primary verification API
        count = queryset.count()
        self.message_user(
            request,
            f'Primary verification triggered for {count} credentials. '
            f'Check the verification status in a few minutes.'
        )
    trigger_primary_verification.short_description = 'Trigger primary source verification'

    def run_ai_analysis(self, request, queryset):
        """Run AI analysis on selected credentials."""
        # This would integrate with the AI verification API
        count = queryset.count()
        self.message_user(
            request,
            f'AI analysis triggered for {count} credentials. '
            f'Check the AI verification summary after processing.'
        )
    run_ai_analysis.short_description = 'Run AI verification analysis'

    def get_urls(self):
        """Add custom admin URLs."""
        urls = super().get_urls()
        custom_urls = [
            path('dashboard/', self.admin_site.admin_view(self.verification_dashboard_view), name='credentials_verification_dashboard'),
            path('queue/', self.admin_site.admin_view(self.verification_queue_view), name='credentials_verification_queue'),
            path('analytics/', self.admin_site.admin_view(self.verification_analytics_view), name='credentials_verification_analytics'),
        ]
        return custom_urls + urls

    def verification_dashboard_view(self, request):
        """Redirect to verification dashboard."""
        from .workflow_views import verification_dashboard
        return verification_dashboard(request)

    def verification_queue_view(self, request):
        """Redirect to verification queue."""
        from .workflow_views import verification_queue
        return verification_queue(request)

    def verification_analytics_view(self, request):
        """Redirect to verification analytics."""
        from .workflow_views import verification_analytics
        return verification_analytics(request)

    def changelist_view(self, request, extra_context=None):
        """Enhanced changelist with workflow links."""
        extra_context = extra_context or {}

        # Add workflow navigation
        extra_context['workflow_links'] = [
            {
                'url': reverse('admin:credentials_verification_dashboard'),
                'title': 'Verification Dashboard',
                'description': 'Real-time verification workflow overview'
            },
            {
                'url': reverse('admin:credentials_verification_queue'),
                'title': 'Verification Queue',
                'description': 'Prioritized credential verification queue'
            },
            {
                'url': reverse('admin:credentials_verification_analytics'),
                'title': 'Analytics',
                'description': 'Verification performance and compliance metrics'
            }
        ]

        # Add quick stats
        extra_context['quick_stats'] = {
            'pending_count': Credential.objects.filter(status=Credential.Status.PENDING).count(),
            'verified_today': Credential.objects.filter(
                status=Credential.Status.VERIFIED,
                verification_date__date=timezone.now().date()
            ).count(),
            'high_priority_count': self._get_high_priority_count()
        }

        return super().changelist_view(request, extra_context)

    def _get_high_priority_count(self):
        """Get count of high priority credentials."""
        # Simplified priority calculation
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)
        return Credential.objects.filter(
            status=Credential.Status.PENDING,
            created_at__lt=week_ago
        ).count()

    class Media:
        js = ('admin/js/credential_admin.js',)
        css = {
            'all': ('admin/css/credential_admin.css',)
        }
