"""
Code Med Talent - Candidate Credential Views
Candidate-facing API endpoints for credential management.
"""

import logging
from typing import Dict, Any

from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from django.utils import timezone
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework.parsers import MultiPartParser, FormParser
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .models import CredentialType, Credential
from .serializers import (
    CredentialTypeSerializer, 
    CredentialSerializer, 
    CredentialUploadSerializer,
    CredentialSummarySerializer,
    CredentialPublicSerializer
)

logger = logging.getLogger(__name__)


class CandidateCredentialViewSet(ModelViewSet):
    """
    ViewSet for candidates to manage their own credentials.
    """
    serializer_class = CredentialSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Candidates can only see their own credentials
        return Credential.objects.filter(user=self.request.user).order_by('-created_at')
    
    def get_serializer_class(self):
        if self.action == 'list':
            return CredentialSummarySerializer
        return CredentialSerializer
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def candidate_dashboard(request):
    """
    Get candidate dashboard data with credential overview.
    """
    try:
        user_credentials = Credential.objects.filter(user=request.user)
        
        # Calculate statistics
        total_credentials = user_credentials.count()
        verified_credentials = user_credentials.filter(status=Credential.Status.VERIFIED).count()
        pending_credentials = user_credentials.filter(status=Credential.Status.PENDING).count()
        expired_credentials = user_credentials.filter(status=Credential.Status.EXPIRED).count()
        
        # Get credentials expiring soon (within 30 days)
        from datetime import date, timedelta
        expiring_soon = user_credentials.filter(
            expiration_date__lte=date.today() + timedelta(days=30),
            expiration_date__gt=date.today(),
            status=Credential.Status.VERIFIED
        ).count()
        
        # Calculate completion rate
        completion_rate = (verified_credentials / total_credentials * 100) if total_credentials > 0 else 0
        
        # Get recent credentials
        recent_credentials = user_credentials.order_by('-created_at')[:5]
        recent_serializer = CredentialSummarySerializer(recent_credentials, many=True)
        
        # Get credentials by category
        category_stats = {}
        for category in CredentialType.Category.choices:
            count = user_credentials.filter(credential_type__category=category[0]).count()
            if count > 0:
                category_stats[category[1]] = count
        
        # Calculate verification score (average AI confidence)
        verified_creds_with_ai = user_credentials.filter(
            status=Credential.Status.VERIFIED,
            ai_extracted_data__isnull=False
        )
        
        total_confidence = 0
        confidence_count = 0
        for cred in verified_creds_with_ai:
            ai_data = cred.ai_extracted_data or {}
            ai_verification = ai_data.get('ai_verification', {})
            confidence = ai_verification.get('overall_confidence', 0)
            if confidence > 0:
                total_confidence += confidence
                confidence_count += 1
        
        verification_score = (total_confidence / confidence_count) if confidence_count > 0 else 0
        
        dashboard_data = {
            'user_info': {
                'name': request.user.get_full_name(),
                'email': request.user.email,
                'user_type': request.user.user_type,
                'date_joined': request.user.date_joined.isoformat()
            },
            'credential_stats': {
                'total_credentials': total_credentials,
                'verified_credentials': verified_credentials,
                'pending_credentials': pending_credentials,
                'expired_credentials': expired_credentials,
                'expiring_soon': expiring_soon,
                'completion_rate': round(completion_rate, 1),
                'verification_score': round(verification_score * 100, 1),
                'by_category': category_stats
            },
            'recent_credentials': recent_serializer.data,
            'next_steps': self._get_next_steps(request.user, user_credentials),
            'alerts': self._get_user_alerts(user_credentials)
        }
        
        return Response(dashboard_data)
        
    except Exception as e:
        logger.error(f"Failed to generate candidate dashboard: {e}")
        return Response(
            {'error': 'Failed to load dashboard data'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def credential_verification_status(request, credential_id):
    """
    Get detailed verification status for a specific credential.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)
    
    try:
        # Get AI verification details
        ai_data = credential.ai_extracted_data or {}
        ai_verification = ai_data.get('ai_verification', {})
        primary_verification = ai_data.get('primary_verification', {})
        
        verification_status = {
            'credential_id': str(credential.id),
            'credential_type': credential.credential_type.name,
            'credential_number': credential.credential_number,
            'current_status': credential.status,
            'status_display': credential.get_status_display(),
            'verification_date': credential.verification_date.isoformat() if credential.verification_date else None,
            'verification_source': credential.verification_source,
            'verification_notes': credential.verification_notes,
            
            # AI Verification Details
            'ai_verification': {
                'overall_confidence': ai_verification.get('overall_confidence', 0),
                'is_likely_valid': ai_verification.get('is_likely_valid', False),
                'verification_recommendation': ai_verification.get('verification_recommendation', 'Unknown'),
                'verification_scores': ai_verification.get('verification_scores', {}),
                'anomalies_detected': ai_verification.get('anomalies_detected', []),
                'risk_factors': ai_verification.get('risk_factors', []),
                'recommendations': ai_verification.get('recommendations', [])
            },
            
            # Primary Source Verification
            'primary_verification': {
                'is_verified': primary_verification.get('is_verified', False),
                'verification_status': primary_verification.get('status', 'Not Verified'),
                'source': primary_verification.get('source', ''),
                'confidence_score': primary_verification.get('confidence_score', 0),
                'details': primary_verification.get('details', {})
            },
            
            # Blockchain Information
            'blockchain': {
                'has_hash': bool(credential.blockchain_hash),
                'hash_value': credential.blockchain_hash[:16] + '...' if credential.blockchain_hash else None,
                'is_on_chain': bool(credential.blockchain_hash)
            },
            
            # Document Information
            'document': {
                'has_document': bool(credential.document_file),
                'ocr_confidence': ai_data.get('ocr_confidence', 0),
                'ocr_method': ai_data.get('ocr_method', 'Unknown'),
                'extraction_confidence': ai_data.get('extraction_confidence', 0)
            },
            
            # Timeline
            'timeline': [
                {
                    'event': 'Credential Uploaded',
                    'date': credential.created_at.isoformat(),
                    'status': 'completed'
                }
            ]
        }
        
        # Add verification events to timeline
        if credential.verification_date:
            verification_status['timeline'].append({
                'event': f'Verification Completed ({credential.get_status_display()})',
                'date': credential.verification_date.isoformat(),
                'status': 'completed'
            })
        
        if credential.blockchain_hash:
            verification_status['timeline'].append({
                'event': 'Stored on Blockchain',
                'date': credential.updated_at.isoformat(),
                'status': 'completed'
            })
        
        # Add next steps
        next_steps = []
        if credential.status == Credential.Status.PENDING:
            next_steps.append('Your credential is being reviewed by our verification team')
            if ai_verification.get('verification_recommendation') == 'MANUAL_REVIEW_REQUIRED':
                next_steps.append('Manual review required due to detected anomalies')
        elif credential.status == Credential.Status.VERIFIED:
            next_steps.append('Your credential is verified and ready to use')
            if not credential.blockchain_hash:
                next_steps.append('Blockchain storage in progress')
        elif credential.status == Credential.Status.INVALID:
            next_steps.append('Please upload a new, valid credential document')
        
        verification_status['next_steps'] = next_steps
        
        return Response(verification_status)
        
    except Exception as e:
        logger.error(f"Failed to get verification status: {e}")
        return Response(
            {'error': 'Failed to get verification status'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def request_verification_update(request, credential_id):
    """
    Request an update on credential verification status.
    """
    credential = get_object_or_404(Credential, id=credential_id, user=request.user)
    
    try:
        # Log the request
        logger.info(f"Verification update requested for credential {credential_id} by user {request.user.id}")
        
        # In a real implementation, this might trigger:
        # - Re-running primary source verification
        # - Notifying admin staff
        # - Updating priority in verification queue
        
        response_data = {
            'credential_id': str(credential.id),
            'message': 'Verification update requested successfully',
            'current_status': credential.status,
            'estimated_completion': 'Within 24-48 hours',
            'request_time': timezone.now().isoformat()
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Failed to request verification update: {e}")
        return Response(
            {'error': 'Failed to request verification update'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def available_credential_types(request):
    """
    Get list of available credential types for upload.
    """
    try:
        # Get active credential types
        credential_types = CredentialType.objects.filter(is_active=True).order_by('category', 'name')
        
        # Group by category
        grouped_types = {}
        for cred_type in credential_types:
            category = cred_type.get_category_display()
            if category not in grouped_types:
                grouped_types[category] = []
            
            grouped_types[category].append({
                'id': str(cred_type.id),
                'name': cred_type.name,
                'description': cred_type.description,
                'verification_source': cred_type.verification_source,
                'has_expiration': cred_type.has_expiration,
                'default_validity_months': cred_type.default_validity_months,
                'verification_url': cred_type.verification_url
            })
        
        return Response({
            'credential_types_by_category': grouped_types,
            'total_types': credential_types.count()
        })
        
    except Exception as e:
        logger.error(f"Failed to get credential types: {e}")
        return Response(
            {'error': 'Failed to load credential types'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _get_next_steps(user, user_credentials):
    """Generate personalized next steps for the user."""
    next_steps = []
    
    total_credentials = user_credentials.count()
    verified_credentials = user_credentials.filter(status=Credential.Status.VERIFIED).count()
    
    if total_credentials == 0:
        next_steps.append({
            'title': 'Upload Your First Credential',
            'description': 'Start by uploading your nursing license or certification',
            'action': 'upload_credential',
            'priority': 'high'
        })
    elif verified_credentials == 0:
        next_steps.append({
            'title': 'Complete Verification Process',
            'description': 'Your uploaded credentials are being verified',
            'action': 'check_status',
            'priority': 'medium'
        })
    else:
        # Check for missing common credentials
        has_license = user_credentials.filter(
            credential_type__name__icontains='license',
            status=Credential.Status.VERIFIED
        ).exists()
        
        has_bls = user_credentials.filter(
            credential_type__name__icontains='bls',
            status=Credential.Status.VERIFIED
        ).exists()
        
        if not has_license:
            next_steps.append({
                'title': 'Upload Professional License',
                'description': 'Add your RN, LPN, or CNA license for better job matches',
                'action': 'upload_license',
                'priority': 'high'
            })
        
        if not has_bls:
            next_steps.append({
                'title': 'Add BLS Certification',
                'description': 'BLS certification is required for most healthcare positions',
                'action': 'upload_bls',
                'priority': 'medium'
            })
        
        # Check for expiring credentials
        from datetime import date, timedelta
        expiring_soon = user_credentials.filter(
            expiration_date__lte=date.today() + timedelta(days=60),
            expiration_date__gt=date.today(),
            status=Credential.Status.VERIFIED
        )
        
        if expiring_soon.exists():
            next_steps.append({
                'title': 'Renew Expiring Credentials',
                'description': f'{expiring_soon.count()} credential(s) expiring within 60 days',
                'action': 'renew_credentials',
                'priority': 'high'
            })
    
    return next_steps


def _get_user_alerts(user_credentials):
    """Generate alerts for the user."""
    alerts = []
    
    # Check for expired credentials
    expired_count = user_credentials.filter(status=Credential.Status.EXPIRED).count()
    if expired_count > 0:
        alerts.append({
            'type': 'warning',
            'title': 'Expired Credentials',
            'message': f'You have {expired_count} expired credential(s). Please upload renewed versions.',
            'action': 'view_expired'
        })
    
    # Check for invalid credentials
    invalid_count = user_credentials.filter(status=Credential.Status.INVALID).count()
    if invalid_count > 0:
        alerts.append({
            'type': 'error',
            'title': 'Invalid Credentials',
            'message': f'{invalid_count} credential(s) could not be verified. Please upload new documents.',
            'action': 'view_invalid'
        })
    
    # Check for pending credentials older than 7 days
    from datetime import timedelta
    old_pending = user_credentials.filter(
        status=Credential.Status.PENDING,
        created_at__lt=timezone.now() - timedelta(days=7)
    ).count()
    
    if old_pending > 0:
        alerts.append({
            'type': 'info',
            'title': 'Verification In Progress',
            'message': f'{old_pending} credential(s) have been pending verification for over 7 days.',
            'action': 'contact_support'
        })
    
    return alerts
