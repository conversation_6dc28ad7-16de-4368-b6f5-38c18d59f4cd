"""
Management command to populate healthcare credential types.
"""

from django.core.management.base import BaseCommand
from django.db import transaction

from apps.credentials.models import CredentialType


class Command(BaseCommand):
    help = 'Populate healthcare credential types for CredentialChain system'

    def handle(self, *args, **options):
        """Populate credential types."""
        
        credential_types_data = [
            # Professional Licenses
            {
                'name': 'Registered Nurse (RN) License',
                'category': CredentialType.Category.LICENSE,
                'description': 'State-issued license to practice as a Registered Nurse',
                'verification_source': 'State Board of Nursing',
                'default_validity_months': 24,
                'verification_url': 'https://www.ncsbn.org/nursys.htm'
            },
            {
                'name': 'Licensed Practical Nurse (LPN) License',
                'category': CredentialType.Category.LICENSE,
                'description': 'State-issued license to practice as a Licensed Practical Nurse',
                'verification_source': 'State Board of Nursing',
                'default_validity_months': 24,
                'verification_url': 'https://www.ncsbn.org/nursys.htm'
            },
            {
                'name': 'Certified Nursing Assistant (CNA) License',
                'category': CredentialType.Category.LICENSE,
                'description': 'State certification to work as a Nursing Assistant',
                'verification_source': 'State Department of Health',
                'default_validity_months': 24,
                'verification_url': ''
            },

            # Life Support Certifications
            {
                'name': 'Basic Life Support (BLS) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'CPR and basic life support certification',
                'verification_source': 'American Heart Association',
                'default_validity_months': 24,
                'verification_url': 'https://cpr.heart.org/en/course-catalog-search'
            },
            {
                'name': 'Advanced Cardiac Life Support (ACLS) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Advanced cardiac life support certification',
                'verification_source': 'American Heart Association',
                'default_validity_months': 24,
                'verification_url': 'https://cpr.heart.org/en/course-catalog-search'
            },
            {
                'name': 'Pediatric Advanced Life Support (PALS) Certification',
                'category': CredentialType.Category.CERTIFICATION,
                'description': 'Pediatric advanced life support certification',
                'verification_source': 'American Heart Association',
                'default_validity_months': 24,
                'verification_url': 'https://cpr.heart.org/en/course-catalog-search'
            },

            # Education Credentials
            {
                'name': 'Bachelor of Science in Nursing (BSN)',
                'category': CredentialType.Category.EDUCATION,
                'description': 'Bachelor\'s degree in nursing',
                'verification_source': 'National Student Clearinghouse',
                'has_expiration': False,
                'verification_url': 'https://www.studentclearinghouse.org/'
            },
            {
                'name': 'Associate Degree in Nursing (ADN)',
                'category': CredentialType.Category.EDUCATION,
                'description': 'Associate degree in nursing',
                'verification_source': 'National Student Clearinghouse',
                'has_expiration': False,
                'verification_url': 'https://www.studentclearinghouse.org/'
            },

            # Background Checks
            {
                'name': 'FBI Background Check',
                'category': CredentialType.Category.BACKGROUND,
                'description': 'Federal criminal background check',
                'verification_source': 'Federal Bureau of Investigation',
                'default_validity_months': 12,
                'verification_url': ''
            },
            {
                'name': 'State Background Check',
                'category': CredentialType.Category.BACKGROUND,
                'description': 'State criminal background check',
                'verification_source': 'State Bureau of Investigation',
                'default_validity_months': 12,
                'verification_url': ''
            },

            # Training
            {
                'name': 'HIPAA Training Certification',
                'category': CredentialType.Category.TRAINING,
                'description': 'Health Insurance Portability and Accountability Act training',
                'verification_source': 'Training Provider',
                'default_validity_months': 12,
                'verification_url': ''
            }
        ]
        
        with transaction.atomic():
            created_count = 0
            updated_count = 0
            
            for cred_data in credential_types_data:
                credential_type, created = CredentialType.objects.get_or_create(
                    name=cred_data['name'],
                    defaults=cred_data
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'Created: {credential_type.name}')
                    )
                else:
                    # Update existing record
                    for key, value in cred_data.items():
                        if key != 'name':  # Don't update the name (unique field)
                            setattr(credential_type, key, value)
                    credential_type.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'Updated: {credential_type.name}')
                    )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nCompleted! Created {created_count} new credential types, '
                f'updated {updated_count} existing ones.'
            )
        )
