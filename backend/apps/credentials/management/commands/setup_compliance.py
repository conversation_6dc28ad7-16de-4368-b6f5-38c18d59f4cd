"""
Management command to set up compliance and security features.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from apps.credentials.compliance import ComplianceAuditLog, compliance_service

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up compliance and security features for CredentialChain'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-test-logs',
            action='store_true',
            help='Create test audit logs for demonstration'
        )
        parser.add_argument(
            '--run-retention-check',
            action='store_true',
            help='Run data retention compliance check'
        )

    def handle(self, *args, **options):
        """Set up compliance features."""
        
        self.stdout.write(
            self.style.SUCCESS('Setting up CredentialChain compliance features...')
        )
        
        # Create compliance audit log table if needed
        self.setup_audit_logging()
        
        # Create test audit logs if requested
        if options['create_test_logs']:
            self.create_test_audit_logs()
        
        # Run retention check if requested
        if options['run_retention_check']:
            self.run_retention_check()
        
        # Display compliance status
        self.display_compliance_status()
        
        self.stdout.write(
            self.style.SUCCESS('Compliance setup completed successfully!')
        )

    def setup_audit_logging(self):
        """Set up audit logging table."""
        try:
            # Check if audit log table exists and has data
            log_count = ComplianceAuditLog.objects.count()
            
            self.stdout.write(f'Audit log table ready with {log_count} existing entries')
            
            # Create initial audit log entry
            ComplianceAuditLog.objects.create(
                user=None,
                user_email='<EMAIL>',
                action='SYSTEM_COMPLIANCE_SETUP',
                resource_type='system',
                resource_id='compliance_module',
                ip_address='127.0.0.1',
                user_agent='Management Command',
                details={
                    'action': 'Compliance module initialized',
                    'timestamp': timezone.now().isoformat(),
                    'features_enabled': [
                        'audit_logging',
                        'data_encryption',
                        'secure_file_storage',
                        'rate_limiting',
                        'security_headers'
                    ]
                },
                risk_level='low'
            )
            
            self.stdout.write(
                self.style.SUCCESS('✓ Audit logging system initialized')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to set up audit logging: {e}')
            )

    def create_test_audit_logs(self):
        """Create test audit logs for demonstration."""
        try:
            self.stdout.write('Creating test audit logs...')
            
            # Get or create test users
            test_users = []
            for i in range(3):
                email = f'testuser{i+1}@example.com'
                user, created = User.objects.get_or_create(
                    email=email,
                    defaults={
                        'first_name': f'Test',
                        'last_name': f'User{i+1}',
                        'user_type': 'candidate'
                    }
                )
                test_users.append(user)
                if created:
                    self.stdout.write(f'Created test user: {email}')
            
            # Create various types of audit logs
            test_activities = [
                {
                    'action': 'CREDENTIAL_UPLOAD',
                    'resource_type': 'credential',
                    'risk_level': 'medium',
                    'details': {'credential_type': 'RN License', 'file_size': '2.5MB'}
                },
                {
                    'action': 'CREDENTIAL_VERIFICATION',
                    'resource_type': 'credential',
                    'risk_level': 'high',
                    'details': {'verification_method': 'primary_source', 'result': 'verified'}
                },
                {
                    'action': 'CANDIDATE_PROFILE_VIEW',
                    'resource_type': 'profile',
                    'risk_level': 'medium',
                    'details': {'viewer_type': 'agency', 'profile_completeness': '85%'}
                },
                {
                    'action': 'BLOCKCHAIN_STORAGE',
                    'resource_type': 'credential',
                    'risk_level': 'low',
                    'details': {'blockchain_network': 'hedera', 'transaction_cost': '0.001'}
                },
                {
                    'action': 'COMPLIANCE_REPORT_GENERATION',
                    'resource_type': 'report',
                    'risk_level': 'medium',
                    'details': {'report_type': 'hipaa_compliance', 'date_range': '30_days'}
                }
            ]
            
            # Create logs for the past 30 days
            for days_ago in range(30):
                log_date = timezone.now() - timedelta(days=days_ago)
                
                # Create 1-5 random activities per day
                import random
                daily_activities = random.randint(1, 5)
                
                for _ in range(daily_activities):
                    user = random.choice(test_users)
                    activity = random.choice(test_activities)
                    
                    ComplianceAuditLog.objects.create(
                        user=user,
                        user_email=user.email,
                        action=activity['action'],
                        resource_type=activity['resource_type'],
                        resource_id=f'test_resource_{random.randint(1000, 9999)}',
                        ip_address=f'192.168.1.{random.randint(1, 254)}',
                        user_agent='Mozilla/5.0 (Test Browser)',
                        details=activity['details'],
                        risk_level=activity['risk_level'],
                        timestamp=log_date
                    )
            
            total_logs = ComplianceAuditLog.objects.count()
            self.stdout.write(
                self.style.SUCCESS(f'✓ Created test audit logs (total: {total_logs})')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create test audit logs: {e}')
            )

    def run_retention_check(self):
        """Run data retention compliance check."""
        try:
            self.stdout.write('Running data retention compliance check...')
            
            result = compliance_service.check_data_retention_compliance()
            
            if 'error' in result:
                self.stdout.write(
                    self.style.ERROR(f'Retention check failed: {result["error"]}')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✓ Retention check completed\n'
                        f'  - Check date: {result["retention_check_date"]}\n'
                        f'  - Logs requiring archival: {result["logs_requiring_archival"]}\n'
                        f'  - Retention policy: {result["retention_policy"]}'
                    )
                )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Retention check failed: {e}')
            )

    def display_compliance_status(self):
        """Display current compliance status."""
        try:
            self.stdout.write('\n' + '='*60)
            self.stdout.write(self.style.SUCCESS('COMPLIANCE STATUS SUMMARY'))
            self.stdout.write('='*60)
            
            # Audit logging status
            log_count = ComplianceAuditLog.objects.count()
            recent_logs = ComplianceAuditLog.objects.filter(
                timestamp__gte=timezone.now() - timedelta(days=7)
            ).count()
            
            self.stdout.write(f'📊 Audit Logging:')
            self.stdout.write(f'   Total audit logs: {log_count}')
            self.stdout.write(f'   Recent logs (7 days): {recent_logs}')
            
            # Risk level breakdown
            high_risk = ComplianceAuditLog.objects.filter(risk_level='high').count()
            medium_risk = ComplianceAuditLog.objects.filter(risk_level='medium').count()
            low_risk = ComplianceAuditLog.objects.filter(risk_level='low').count()
            
            self.stdout.write(f'   Risk levels: High({high_risk}) Medium({medium_risk}) Low({low_risk})')
            
            # Security features status
            self.stdout.write(f'\n🔒 Security Features:')
            self.stdout.write(f'   ✓ Data encryption enabled')
            self.stdout.write(f'   ✓ Secure file storage configured')
            self.stdout.write(f'   ✓ Rate limiting middleware active')
            self.stdout.write(f'   ✓ Security headers middleware active')
            self.stdout.write(f'   ✓ HIPAA compliance middleware active')
            
            # Compliance features
            self.stdout.write(f'\n📋 Compliance Features:')
            self.stdout.write(f'   ✓ HIPAA audit trail')
            self.stdout.write(f'   ✓ Data retention policies')
            self.stdout.write(f'   ✓ Access logging')
            self.stdout.write(f'   ✓ Sensitive data encryption')
            self.stdout.write(f'   ✓ Compliance reporting')
            
            # Next steps
            self.stdout.write(f'\n📝 Next Steps:')
            self.stdout.write(f'   1. Configure middleware in Django settings')
            self.stdout.write(f'   2. Set up secure cloud storage for documents')
            self.stdout.write(f'   3. Configure log rotation and archival')
            self.stdout.write(f'   4. Set up monitoring and alerting')
            self.stdout.write(f'   5. Schedule regular compliance reports')
            
            self.stdout.write('\n' + '='*60)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to display compliance status: {e}')
            )
