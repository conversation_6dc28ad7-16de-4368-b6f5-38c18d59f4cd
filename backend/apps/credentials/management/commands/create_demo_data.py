"""
Management command to create demo data for testing the CredentialChain system.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from datetime import datetime, timedelta
import random

from apps.credentials.models import CredentialType, Credential

User = get_user_model()


class Command(BaseCommand):
    help = 'Create demo data for testing the CredentialChain system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=5,
            help='Number of demo users to create'
        )
        parser.add_argument(
            '--credentials',
            type=int,
            default=20,
            help='Number of demo credentials to create'
        )

    def handle(self, *args, **options):
        """Create demo data."""
        
        with transaction.atomic():
            # Create demo users
            demo_users = self.create_demo_users(options['users'])
            
            # Create demo credentials
            demo_credentials = self.create_demo_credentials(demo_users, options['credentials'])
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Demo data created successfully!\n'
                    f'- {len(demo_users)} demo users\n'
                    f'- {len(demo_credentials)} demo credentials\n'
                    f'\nYou can now test the admin interface at /admin/'
                )
            )

    def create_demo_users(self, count):
        """Create demo users."""
        demo_users = []
        
        # Sample user data
        user_data = [
            {'first_name': 'John', 'last_name': 'Doe', 'email': '<EMAIL>'},
            {'first_name': 'Jane', 'last_name': 'Smith', 'email': '<EMAIL>'},
            {'first_name': 'Michael', 'last_name': 'Johnson', 'email': '<EMAIL>'},
            {'first_name': 'Sarah', 'last_name': 'Williams', 'email': '<EMAIL>'},
            {'first_name': 'David', 'last_name': 'Brown', 'email': '<EMAIL>'},
            {'first_name': 'Emily', 'last_name': 'Davis', 'email': '<EMAIL>'},
            {'first_name': 'Robert', 'last_name': 'Miller', 'email': '<EMAIL>'},
            {'first_name': 'Lisa', 'last_name': 'Wilson', 'email': '<EMAIL>'},
        ]
        
        for i in range(min(count, len(user_data))):
            data = user_data[i]
            
            # Check if user already exists
            if User.objects.filter(email=data['email']).exists():
                user = User.objects.get(email=data['email'])
                self.stdout.write(f'User {data["email"]} already exists, skipping...')
            else:
                user = User.objects.create_user(
                    email=data['email'],
                    password='demo123',
                    first_name=data['first_name'],
                    last_name=data['last_name'],
                    user_type='candidate'
                )
                self.stdout.write(f'Created user: {user.email}')
            
            demo_users.append(user)
        
        return demo_users

    def create_demo_credentials(self, users, count):
        """Create demo credentials."""
        demo_credentials = []
        
        # Get credential types
        credential_types = list(CredentialType.objects.all())
        if not credential_types:
            self.stdout.write(
                self.style.ERROR(
                    'No credential types found. Please run populate_credential_types first.'
                )
            )
            return []
        
        # Sample credential data
        sample_data = {
            'Registered Nurse (RN) License': [
                {'number': 'RN123456', 'authority': 'California Board of Nursing', 'state': 'CA'},
                {'number': 'RN789012', 'authority': 'Texas Board of Nursing', 'state': 'TX'},
                {'number': 'RN345678', 'authority': 'New York Board of Nursing', 'state': 'NY'},
                {'number': 'RN901234', 'authority': 'Florida Board of Nursing', 'state': 'FL'},
            ],
            'Basic Life Support (BLS) Certification': [
                {'number': 'BLS123456', 'authority': 'American Heart Association'},
                {'number': 'BLS789012', 'authority': 'American Heart Association'},
                {'number': 'BLS345678', 'authority': 'American Red Cross'},
            ],
            'Advanced Cardiac Life Support (ACLS) Certification': [
                {'number': 'ACLS123456', 'authority': 'American Heart Association'},
                {'number': 'ACLS789012', 'authority': 'American Heart Association'},
            ],
            'Bachelor of Science in Nursing (BSN)': [
                {'authority': 'University of California, Los Angeles'},
                {'authority': 'University of Texas at Austin'},
                {'authority': 'New York University'},
            ]
        }
        
        statuses = [
            Credential.Status.PENDING,
            Credential.Status.VERIFIED,
            Credential.Status.INVALID,
            Credential.Status.EXPIRED
        ]
        
        for i in range(count):
            user = random.choice(users)
            credential_type = random.choice(credential_types)
            
            # Get sample data for this credential type
            type_samples = sample_data.get(credential_type.name, [{'number': f'DEMO{i:06d}', 'authority': 'Demo Authority'}])
            sample = random.choice(type_samples)
            
            # Generate dates
            issue_date = timezone.now().date() - timedelta(days=random.randint(30, 730))
            
            if credential_type.has_expiration:
                expiration_date = issue_date + timedelta(days=credential_type.default_validity_months * 30)
            else:
                expiration_date = None
            
            # Create credential
            credential = Credential.objects.create(
                user=user,
                credential_type=credential_type,
                credential_number=sample.get('number', f'DEMO{i:06d}'),
                holder_name=user.get_full_name(),
                issuing_authority=sample.get('authority', 'Demo Authority'),
                issuing_state=sample.get('state', ''),
                issue_date=issue_date,
                expiration_date=expiration_date,
                status=random.choice(statuses),
                verification_source='Demo Data',
                verification_notes=f'Demo credential created for testing - {timezone.now()}',
                ai_extracted_data={
                    'ocr_text': f'Demo OCR text for {credential_type.name}',
                    'ocr_confidence': random.uniform(0.7, 0.95),
                    'extraction_confidence': random.uniform(0.6, 0.9),
                    'ai_verification': {
                        'overall_confidence': random.uniform(0.5, 0.95),
                        'is_likely_valid': random.choice([True, False]),
                        'verification_recommendation': random.choice([
                            'AUTO_APPROVE', 'APPROVE_WITH_REVIEW', 
                            'MANUAL_REVIEW_REQUIRED', 'REJECT_OR_RESUBMIT'
                        ]),
                        'anomalies_detected': random.choice([
                            [],
                            ['Minor OCR artifacts'],
                            ['Date format inconsistency'],
                            ['Name mismatch', 'Unusual document format']
                        ]),
                        'risk_factors': random.choice([
                            [],
                            ['Low OCR confidence'],
                            ['Missing expiration date'],
                            ['Anomaly detected', 'Low data completeness']
                        ])
                    }
                }
            )
            
            # Set verification date for verified credentials
            if credential.status == Credential.Status.VERIFIED:
                credential.verification_date = timezone.now()
                credential.blockchain_hash = f'demo_hash_{credential.id}_{random.randint(1000, 9999)}'
                credential.save()
            
            demo_credentials.append(credential)
            
            self.stdout.write(f'Created credential: {credential_type.name} for {user.email}')
        
        return demo_credentials
