"""
Management command to test the enhanced verification workflow.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from datetime import timedelta
import random

from apps.credentials.models import CredentialType, Credential
from apps.credentials.compliance import compliance_service

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the enhanced verification workflow with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-admin',
            action='store_true',
            help='Create admin user for testing'
        )
        parser.add_argument(
            '--simulate-workflow',
            action='store_true',
            help='Simulate verification workflow activities'
        )
        parser.add_argument(
            '--test-dashboard',
            action='store_true',
            help='Test dashboard functionality'
        )

    def handle(self, *args, **options):
        """Test the verification workflow."""
        
        self.stdout.write(
            self.style.SUCCESS('Testing Enhanced Verification Workflow...')
        )
        
        if options['create_admin']:
            self.create_admin_user()
        
        if options['simulate_workflow']:
            self.simulate_workflow()
        
        if options['test_dashboard']:
            self.test_dashboard()
        
        self.display_workflow_status()
        
        self.stdout.write(
            self.style.SUCCESS('Workflow testing completed!')
        )

    def create_admin_user(self):
        """Create admin user for testing."""
        try:
            admin_email = '<EMAIL>'
            
            if User.objects.filter(email=admin_email).exists():
                self.stdout.write(f'Admin user {admin_email} already exists')
                return
            
            admin_user = User.objects.create_user(
                email=admin_email,
                password='admin123',
                first_name='Admin',
                last_name='User',
                user_type='admin',
                is_staff=True,
                is_superuser=True
            )
            
            self.stdout.write(
                self.style.SUCCESS(f'✓ Created admin user: {admin_email} (password: admin123)')
            )
            
            # Log admin creation
            compliance_service.log_access(
                user=admin_user,
                action='ADMIN_USER_CREATED',
                resource_type='user',
                resource_id=str(admin_user.id),
                request=None,  # Mock request
                details={
                    'created_by': 'management_command',
                    'purpose': 'workflow_testing'
                },
                risk_level='high'
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create admin user: {e}')
            )

    def simulate_workflow(self):
        """Simulate verification workflow activities."""
        try:
            self.stdout.write('Simulating verification workflow...')
            
            # Get or create test credentials
            credentials = Credential.objects.all()[:10]
            
            if not credentials:
                self.stdout.write('No credentials found. Run create_demo_data first.')
                return
            
            # Get admin user
            admin_user = User.objects.filter(is_staff=True).first()
            if not admin_user:
                self.stdout.write('No admin user found. Use --create-admin first.')
                return
            
            # Simulate verification activities
            activities = [
                ('verify', 'Verified after manual review'),
                ('reject', 'Document quality insufficient'),
                ('verify', 'Primary source confirmed'),
                ('flag', 'Requires additional documentation'),
                ('verify', 'AI analysis confirmed authenticity')
            ]
            
            for i, credential in enumerate(credentials[:5]):
                action, notes = random.choice(activities)
                
                if action == 'verify':
                    credential.status = Credential.Status.VERIFIED
                    credential.verified_by = admin_user
                    credential.verification_date = timezone.now()
                elif action == 'reject':
                    credential.status = Credential.Status.INVALID
                    credential.verified_by = admin_user
                    credential.verification_date = timezone.now()
                
                credential.verification_notes = f"{notes} (Simulated workflow test)"
                credential.save()
                
                # Log the activity
                compliance_service.log_access(
                    user=admin_user,
                    action=f'WORKFLOW_TEST_{action.upper()}',
                    resource_type='credential',
                    resource_id=str(credential.id),
                    request=None,
                    details={
                        'credential_type': credential.credential_type.name,
                        'action': action,
                        'notes': notes,
                        'simulation': True
                    },
                    risk_level='medium'
                )
                
                self.stdout.write(f'  ✓ {action.title()}ed credential {credential.id}')
            
            self.stdout.write(
                self.style.SUCCESS('✓ Workflow simulation completed')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Workflow simulation failed: {e}')
            )

    def test_dashboard(self):
        """Test dashboard functionality."""
        try:
            self.stdout.write('Testing dashboard functionality...')
            
            # Import dashboard functions
            from apps.credentials.workflow_views import (
                _get_dashboard_stats,
                _get_queue_metrics,
                _get_recent_activity
            )
            
            # Test dashboard stats
            stats = _get_dashboard_stats()
            self.stdout.write(f'  ✓ Dashboard stats: {stats}')
            
            # Test queue metrics
            queue_metrics = _get_queue_metrics()
            self.stdout.write(f'  ✓ Queue metrics: {queue_metrics}')
            
            # Test recent activity
            recent_activity = _get_recent_activity()
            self.stdout.write(f'  ✓ Recent activity: {len(recent_activity)} entries')
            
            # Test priority calculation
            from apps.credentials.workflow_views import _calculate_priority_score
            
            credentials = Credential.objects.filter(status=Credential.Status.PENDING)[:3]
            for credential in credentials:
                priority = _calculate_priority_score(credential)
                self.stdout.write(f'  ✓ Credential {credential.id} priority: {priority}')
            
            self.stdout.write(
                self.style.SUCCESS('✓ Dashboard functionality test completed')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Dashboard test failed: {e}')
            )

    def display_workflow_status(self):
        """Display current workflow status."""
        try:
            self.stdout.write('\n' + '='*60)
            self.stdout.write(self.style.SUCCESS('VERIFICATION WORKFLOW STATUS'))
            self.stdout.write('='*60)
            
            # Credential statistics
            total_credentials = Credential.objects.count()
            pending_count = Credential.objects.filter(status=Credential.Status.PENDING).count()
            verified_count = Credential.objects.filter(status=Credential.Status.VERIFIED).count()
            invalid_count = Credential.objects.filter(status=Credential.Status.INVALID).count()
            
            self.stdout.write(f'📊 Credential Statistics:')
            self.stdout.write(f'   Total credentials: {total_credentials}')
            self.stdout.write(f'   Pending review: {pending_count}')
            self.stdout.write(f'   Verified: {verified_count}')
            self.stdout.write(f'   Invalid/Rejected: {invalid_count}')
            
            # Verification rate
            verification_rate = (verified_count / total_credentials * 100) if total_credentials > 0 else 0
            self.stdout.write(f'   Verification rate: {verification_rate:.1f}%')
            
            # Admin users
            admin_count = User.objects.filter(is_staff=True).count()
            self.stdout.write(f'\n👥 Admin Users: {admin_count}')
            
            # Recent activity
            from apps.credentials.compliance import ComplianceAuditLog
            recent_logs = ComplianceAuditLog.objects.filter(
                timestamp__gte=timezone.now() - timedelta(hours=24)
            ).count()
            self.stdout.write(f'📝 Recent Activity (24h): {recent_logs} audit log entries')
            
            # Workflow URLs
            self.stdout.write(f'\n🔗 Workflow URLs:')
            self.stdout.write(f'   Dashboard: /admin/credentials/dashboard/')
            self.stdout.write(f'   Queue: /admin/credentials/queue/')
            self.stdout.write(f'   Analytics: /admin/credentials/analytics/')
            self.stdout.write(f'   Admin: /admin/credentials/credential/')
            
            # Next steps
            self.stdout.write(f'\n📋 Next Steps:')
            if admin_count == 0:
                self.stdout.write(f'   1. Create admin user: --create-admin')
            else:
                self.stdout.write(f'   1. ✓ Admin user available')
            
            if total_credentials == 0:
                self.stdout.write(f'   2. Create demo data: python manage.py create_demo_data')
            else:
                self.stdout.write(f'   2. ✓ Demo data available')
            
            if pending_count == 0:
                self.stdout.write(f'   3. Simulate workflow: --simulate-workflow')
            else:
                self.stdout.write(f'   3. ✓ Pending credentials available for testing')
            
            self.stdout.write(f'   4. Access dashboard at: http://localhost:8000/admin/credentials/dashboard/')
            self.stdout.write(f'   5. Test verification queue and analytics')
            
            self.stdout.write('\n' + '='*60)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to display workflow status: {e}')
            )
