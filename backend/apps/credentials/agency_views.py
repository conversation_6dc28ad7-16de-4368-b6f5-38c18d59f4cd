"""
Code Med Talent - Agency Integration API
API endpoints for healthcare agencies to access verified candidate credentials.
"""

import logging
from typing import Dict, Any, List
from datetime import date, timedelta

from django.shortcuts import get_object_or_404
from django.db.models import Q, Count, Avg
from django.contrib.auth import get_user_model
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.pagination import PageNumberPagination
from drf_spectacular.utils import extend_schema, OpenApiParameter

from .models import CredentialType, Credential
from .serializers import CredentialPublicSerializer, CredentialStatsSerializer

User = get_user_model()
logger = logging.getLogger(__name__)


class AgencyPermission(permissions.BasePermission):
    """
    Custom permission for agency users.
    """
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            request.user.user_type in ['agency', 'facility', 'admin']
        )


class CredentialPagination(PageNumberPagination):
    """Custom pagination for credential listings."""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


@api_view(['GET'])
@permission_classes([AgencyPermission])
def verified_candidates_search(request):
    """
    Search for candidates with verified credentials.
    """
    try:
        # Get query parameters
        credential_types = request.GET.getlist('credential_type')
        specialties = request.GET.getlist('specialty')
        states = request.GET.getlist('state')
        min_experience = request.GET.get('min_experience')
        availability = request.GET.get('availability')
        search_query = request.GET.get('q', '')
        
        # Start with verified credentials only
        credentials = Credential.objects.filter(
            status=Credential.Status.VERIFIED
        ).select_related('user', 'credential_type')
        
        # Apply filters
        if credential_types:
            credentials = credentials.filter(credential_type__name__in=credential_types)
        
        if states:
            credentials = credentials.filter(issuing_state__in=states)
        
        if search_query:
            credentials = credentials.filter(
                Q(holder_name__icontains=search_query) |
                Q(credential_type__name__icontains=search_query) |
                Q(issuing_authority__icontains=search_query)
            )
        
        # Group by user to get unique candidates
        candidate_data = {}
        for credential in credentials:
            user_id = credential.user.id
            if user_id not in candidate_data:
                candidate_data[user_id] = {
                    'candidate_id': str(user_id),
                    'name': credential.holder_name or credential.user.get_full_name(),
                    'email': credential.user.email,
                    'credentials': [],
                    'verification_score': 0,
                    'total_credentials': 0,
                    'last_verified': None
                }
            
            # Add credential to candidate
            cred_data = {
                'id': str(credential.id),
                'type': credential.credential_type.name,
                'category': credential.credential_type.get_category_display(),
                'number': credential.credential_number,
                'issuing_authority': credential.issuing_authority,
                'verification_date': credential.verification_date.isoformat() if credential.verification_date else None,
                'expiration_date': credential.expiration_date.isoformat() if credential.expiration_date else None,
                'is_expired': credential.is_expired(),
                'blockchain_verified': bool(credential.blockchain_hash)
            }
            candidate_data[user_id]['credentials'].append(cred_data)
            candidate_data[user_id]['total_credentials'] += 1
            
            # Update verification score and last verified date
            if credential.verification_date:
                if (not candidate_data[user_id]['last_verified'] or 
                    credential.verification_date.isoformat() > candidate_data[user_id]['last_verified']):
                    candidate_data[user_id]['last_verified'] = credential.verification_date.isoformat()
        
        # Calculate verification scores
        for candidate in candidate_data.values():
            if candidate['credentials']:
                # Simple scoring based on number of verified credentials and recency
                base_score = min(candidate['total_credentials'] * 20, 80)  # Max 80 for credentials
                
                # Bonus for recent verification
                if candidate['last_verified']:
                    from datetime import datetime
                    last_verified = datetime.fromisoformat(candidate['last_verified'].replace('Z', '+00:00'))
                    days_since = (datetime.now(last_verified.tzinfo) - last_verified).days
                    recency_bonus = max(20 - (days_since / 30), 0)  # Max 20 bonus, decreases over time
                    candidate['verification_score'] = min(base_score + recency_bonus, 100)
                else:
                    candidate['verification_score'] = base_score
        
        # Sort by verification score
        candidates_list = sorted(
            candidate_data.values(), 
            key=lambda x: x['verification_score'], 
            reverse=True
        )
        
        # Pagination
        paginator = CredentialPagination()
        page = paginator.paginate_queryset(candidates_list, request)
        
        response_data = {
            'candidates': page,
            'total_candidates': len(candidates_list),
            'search_filters': {
                'credential_types': credential_types,
                'states': states,
                'search_query': search_query
            }
        }
        
        return paginator.get_paginated_response(response_data)
        
    except Exception as e:
        logger.error(f"Candidate search failed: {e}")
        return Response(
            {'error': 'Failed to search candidates'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AgencyPermission])
def candidate_credential_profile(request, candidate_id):
    """
    Get detailed credential profile for a specific candidate.
    """
    try:
        # Get candidate user
        candidate = get_object_or_404(User, id=candidate_id, user_type='candidate')
        
        # Get all verified credentials
        verified_credentials = Credential.objects.filter(
            user=candidate,
            status=Credential.Status.VERIFIED
        ).select_related('credential_type').order_by('-verification_date')
        
        if not verified_credentials.exists():
            return Response(
                {'error': 'No verified credentials found for this candidate'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Serialize credentials
        credentials_serializer = CredentialPublicSerializer(verified_credentials, many=True)
        
        # Calculate profile statistics
        total_credentials = verified_credentials.count()
        
        # Group by category
        by_category = {}
        for credential in verified_credentials:
            category = credential.credential_type.get_category_display()
            if category not in by_category:
                by_category[category] = 0
            by_category[category] += 1
        
        # Check for expiring credentials
        expiring_soon = verified_credentials.filter(
            expiration_date__lte=date.today() + timedelta(days=90),
            expiration_date__gt=date.today()
        ).count()
        
        # Calculate verification confidence
        total_confidence = 0
        confidence_count = 0
        for cred in verified_credentials:
            if cred.ai_extracted_data and 'ai_verification' in cred.ai_extracted_data:
                confidence = cred.ai_extracted_data['ai_verification'].get('overall_confidence', 0)
                if confidence > 0:
                    total_confidence += confidence
                    confidence_count += 1
        
        avg_confidence = (total_confidence / confidence_count) if confidence_count > 0 else 0
        
        # Check blockchain verification
        blockchain_verified = verified_credentials.filter(blockchain_hash__isnull=False).count()
        
        profile_data = {
            'candidate_info': {
                'candidate_id': str(candidate.id),
                'name': verified_credentials.first().holder_name or candidate.get_full_name(),
                'email': candidate.email,
                'profile_created': candidate.date_joined.isoformat(),
                'last_credential_verified': verified_credentials.first().verification_date.isoformat() if verified_credentials.first().verification_date else None
            },
            'credential_summary': {
                'total_verified_credentials': total_credentials,
                'credentials_by_category': by_category,
                'expiring_within_90_days': expiring_soon,
                'average_verification_confidence': round(avg_confidence * 100, 1),
                'blockchain_verified_count': blockchain_verified,
                'blockchain_verification_rate': round((blockchain_verified / total_credentials) * 100, 1) if total_credentials > 0 else 0
            },
            'credentials': credentials_serializer.data,
            'verification_badges': self._generate_verification_badges(verified_credentials),
            'compliance_status': self._check_compliance_status(verified_credentials)
        }
        
        return Response(profile_data)
        
    except Exception as e:
        logger.error(f"Failed to get candidate profile: {e}")
        return Response(
            {'error': 'Failed to load candidate profile'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AgencyPermission])
def credential_verification_report(request, credential_id):
    """
    Get detailed verification report for a specific credential.
    """
    try:
        credential = get_object_or_404(
            Credential, 
            id=credential_id, 
            status=Credential.Status.VERIFIED
        )
        
        # Get verification details
        ai_data = credential.ai_extracted_data or {}
        ai_verification = ai_data.get('ai_verification', {})
        primary_verification = ai_data.get('primary_verification', {})
        
        verification_report = {
            'credential_info': {
                'id': str(credential.id),
                'type': credential.credential_type.name,
                'category': credential.credential_type.get_category_display(),
                'number': credential.credential_number,
                'holder_name': credential.holder_name,
                'issuing_authority': credential.issuing_authority,
                'issue_date': credential.issue_date.isoformat() if credential.issue_date else None,
                'expiration_date': credential.expiration_date.isoformat() if credential.expiration_date else None,
                'is_expired': credential.is_expired()
            },
            'verification_details': {
                'verification_date': credential.verification_date.isoformat() if credential.verification_date else None,
                'verification_source': credential.verification_source,
                'verified_by': credential.verified_by.get_full_name() if credential.verified_by else None,
                'verification_method': 'Automated + Manual Review'
            },
            'ai_analysis': {
                'overall_confidence': ai_verification.get('overall_confidence', 0),
                'verification_recommendation': ai_verification.get('verification_recommendation', 'Unknown'),
                'verification_scores': ai_verification.get('verification_scores', {}),
                'anomalies_detected': len(ai_verification.get('anomalies_detected', [])),
                'risk_factors': len(ai_verification.get('risk_factors', []))
            },
            'primary_source_verification': {
                'is_verified': primary_verification.get('is_verified', False),
                'source': primary_verification.get('source', 'Not Available'),
                'confidence_score': primary_verification.get('confidence_score', 0),
                'verification_status': primary_verification.get('status', 'Not Verified')
            },
            'blockchain_verification': {
                'is_on_blockchain': bool(credential.blockchain_hash),
                'hash_preview': credential.blockchain_hash[:16] + '...' if credential.blockchain_hash else None,
                'tamper_proof': bool(credential.blockchain_hash),
                'verification_immutable': bool(credential.blockchain_hash)
            },
            'compliance_indicators': {
                'meets_verification_standards': ai_verification.get('overall_confidence', 0) >= 0.8,
                'primary_source_confirmed': primary_verification.get('is_verified', False),
                'blockchain_secured': bool(credential.blockchain_hash),
                'manual_review_completed': bool(credential.verified_by),
                'document_integrity_verified': ai_verification.get('verification_scores', {}).get('ocr_quality', 0) >= 0.7
            }
        }
        
        return Response(verification_report)
        
    except Exception as e:
        logger.error(f"Failed to generate verification report: {e}")
        return Response(
            {'error': 'Failed to generate verification report'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([AgencyPermission])
def agency_analytics(request):
    """
    Get analytics and insights for agency dashboard.
    """
    try:
        # Get all verified credentials
        verified_credentials = Credential.objects.filter(status=Credential.Status.VERIFIED)
        
        # Calculate statistics
        total_verified = verified_credentials.count()
        unique_candidates = verified_credentials.values('user').distinct().count()
        
        # Credentials by type
        by_type = {}
        for cred_type in CredentialType.objects.all():
            count = verified_credentials.filter(credential_type=cred_type).count()
            if count > 0:
                by_type[cred_type.name] = count
        
        # Credentials by state
        by_state = {}
        state_data = verified_credentials.values('issuing_state').annotate(count=Count('id'))
        for item in state_data:
            if item['issuing_state']:
                by_state[item['issuing_state']] = item['count']
        
        # Recent verification trends (last 30 days)
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_verifications = verified_credentials.filter(
            verification_date__gte=thirty_days_ago
        ).count()
        
        # Average verification confidence
        ai_confidences = []
        for cred in verified_credentials:
            if cred.ai_extracted_data and 'ai_verification' in cred.ai_extracted_data:
                confidence = cred.ai_extracted_data['ai_verification'].get('overall_confidence', 0)
                if confidence > 0:
                    ai_confidences.append(confidence)
        
        avg_confidence = sum(ai_confidences) / len(ai_confidences) if ai_confidences else 0
        
        # Blockchain adoption
        blockchain_count = verified_credentials.filter(blockchain_hash__isnull=False).count()
        blockchain_rate = (blockchain_count / total_verified) * 100 if total_verified > 0 else 0
        
        analytics_data = {
            'overview': {
                'total_verified_credentials': total_verified,
                'unique_verified_candidates': unique_candidates,
                'recent_verifications_30_days': recent_verifications,
                'average_verification_confidence': round(avg_confidence * 100, 1),
                'blockchain_adoption_rate': round(blockchain_rate, 1)
            },
            'credentials_by_type': by_type,
            'credentials_by_state': by_state,
            'quality_metrics': {
                'high_confidence_credentials': len([c for c in ai_confidences if c >= 0.9]),
                'medium_confidence_credentials': len([c for c in ai_confidences if 0.7 <= c < 0.9]),
                'low_confidence_credentials': len([c for c in ai_confidences if c < 0.7]),
                'blockchain_secured_credentials': blockchain_count
            },
            'generated_at': datetime.now().isoformat()
        }
        
        return Response(analytics_data)
        
    except Exception as e:
        logger.error(f"Failed to generate analytics: {e}")
        return Response(
            {'error': 'Failed to generate analytics'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def _generate_verification_badges(credentials):
    """Generate verification badges for a candidate."""
    badges = []
    
    # Check for different types of credentials
    has_license = credentials.filter(credential_type__category=CredentialType.Category.LICENSE).exists()
    has_certification = credentials.filter(credential_type__category=CredentialType.Category.CERTIFICATION).exists()
    has_education = credentials.filter(credential_type__category=CredentialType.Category.EDUCATION).exists()
    
    if has_license:
        badges.append({
            'name': 'Licensed Professional',
            'description': 'Has verified professional license',
            'color': '#28a745'
        })
    
    if has_certification:
        badges.append({
            'name': 'Certified',
            'description': 'Has verified certifications',
            'color': '#007bff'
        })
    
    if has_education:
        badges.append({
            'name': 'Educated',
            'description': 'Has verified educational credentials',
            'color': '#6f42c1'
        })
    
    # Check for blockchain verification
    blockchain_count = credentials.filter(blockchain_hash__isnull=False).count()
    if blockchain_count > 0:
        badges.append({
            'name': 'Blockchain Verified',
            'description': f'{blockchain_count} credential(s) secured on blockchain',
            'color': '#fd7e14'
        })
    
    return badges


def _check_compliance_status(credentials):
    """Check compliance status for common healthcare requirements."""
    compliance = {
        'basic_requirements_met': False,
        'advanced_certifications': False,
        'education_verified': False,
        'all_current': True,
        'details': []
    }
    
    # Check for basic requirements (license + BLS)
    has_license = credentials.filter(credential_type__category=CredentialType.Category.LICENSE).exists()
    has_bls = credentials.filter(credential_type__name__icontains='bls').exists()
    
    compliance['basic_requirements_met'] = has_license and has_bls
    
    if has_license:
        compliance['details'].append('✓ Professional license verified')
    else:
        compliance['details'].append('✗ Professional license required')
    
    if has_bls:
        compliance['details'].append('✓ BLS certification verified')
    else:
        compliance['details'].append('✗ BLS certification required')
    
    # Check for advanced certifications
    advanced_certs = credentials.filter(
        credential_type__name__icontains__in=['acls', 'pals', 'ccrn', 'cen']
    )
    compliance['advanced_certifications'] = advanced_certs.exists()
    
    # Check for education
    compliance['education_verified'] = credentials.filter(
        credential_type__category=CredentialType.Category.EDUCATION
    ).exists()
    
    # Check if all credentials are current
    expired_count = sum(1 for cred in credentials if cred.is_expired())
    compliance['all_current'] = expired_count == 0
    
    if expired_count > 0:
        compliance['details'].append(f'⚠ {expired_count} expired credential(s)')
    
    return compliance
