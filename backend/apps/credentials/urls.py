"""
Code Med Talent - Credentials URLs
URL patterns for CredentialChain API endpoints.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer

from .views import (
    CredentialTypeViewSet,
    CredentialViewSet,
    CredentialUploadView,
    CredentialVerificationView,
    credential_stats,
    reprocess_credential,
    verify_credential_primary_source,
    run_ai_verification,
    create_career_passport,
    get_career_passport_report,
    verify_blockchain_credential,
    compliance_report,
    data_retention_check
)
from .candidate_views import (
    CandidateCredentialViewSet,
    candidate_dashboard,
    credential_verification_status,
    request_verification_update,
    available_credential_types
)
from .agency_views import (
    verified_candidates_search,
    candidate_credential_profile,
    credential_verification_report,
    agency_analytics
)
from .workflow_views import (
    verification_dashboard,
    verification_queue,
    quick_verify_credential,
    verification_analytics,
    ajax_dashboard_stats
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r'types', CredentialTypeViewSet, basename='credential-types')
router.register(r'credentials', CredentialViewSet, basename='credentials')
router.register(r'my-credentials', CandidateCredentialViewSet, basename='my-credentials')

app_name = 'credentials'

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Custom API endpoints
    path('upload/', CredentialUploadView.as_view(), name='credential-upload'),
    path('verify/<uuid:credential_id>/', CredentialVerificationView.as_view(), name='credential-verify'),
    path('verify-primary/<uuid:credential_id>/', verify_credential_primary_source, name='credential-verify-primary'),
    path('verify-ai/<uuid:credential_id>/', run_ai_verification, name='credential-verify-ai'),
    path('verify-blockchain/<uuid:credential_id>/', verify_blockchain_credential, name='credential-verify-blockchain'),
    path('stats/', credential_stats, name='credential-stats'),
    path('reprocess/<uuid:credential_id>/', reprocess_credential, name='credential-reprocess'),

    # Career Passport endpoints
    path('career-passport/create/', create_career_passport, name='career-passport-create'),
    path('career-passport/report/', get_career_passport_report, name='career-passport-report'),

    # Candidate-specific endpoints
    path('dashboard/', candidate_dashboard, name='candidate-dashboard'),
    path('status/<uuid:credential_id>/', credential_verification_status, name='credential-status'),
    path('request-update/<uuid:credential_id>/', request_verification_update, name='request-verification-update'),
    path('available-types/', available_credential_types, name='available-credential-types'),

    # Agency/Facility endpoints
    path('agency/search/', verified_candidates_search, name='agency-candidate-search'),
    path('agency/candidate/<uuid:candidate_id>/', candidate_credential_profile, name='agency-candidate-profile'),
    path('agency/verification-report/<uuid:credential_id>/', credential_verification_report, name='agency-verification-report'),
    path('agency/analytics/', agency_analytics, name='agency-analytics'),

    # Compliance endpoints (admin only)
    path('compliance/report/', compliance_report, name='compliance-report'),
    path('compliance/data-retention/', data_retention_check, name='data-retention-check'),

    # Admin workflow endpoints
    path('admin/dashboard/', verification_dashboard, name='verification-dashboard'),
    path('admin/queue/', verification_queue, name='verification-queue'),
    path('admin/analytics/', verification_analytics, name='verification-analytics'),
    path('admin/quick-verify/<uuid:credential_id>/', quick_verify_credential, name='quick-verify-credential'),
    path('admin/ajax/dashboard-stats/', ajax_dashboard_stats, name='ajax-dashboard-stats'),
]
